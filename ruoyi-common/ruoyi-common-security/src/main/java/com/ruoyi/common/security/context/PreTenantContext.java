package com.ruoyi.common.security.context;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.function.Function;

@Component
public class PreTenantContext {
    private static final String KEY_CURRENT_PROVIDER_TENANT_ID = "KEY_CURRENT_PROVIDER_TENANT_ID";
    private static final String KEY_CURRENT_PROVIDER_APP_ID = "KEY_CURRENT_PROVIDER_APP_ID";
    private static final ConcurrentMap<String, String> mContext = new ConcurrentHashMap();


    public void setCurrentTenantId(String tenantId) {
        mContext.put(KEY_CURRENT_PROVIDER_TENANT_ID, tenantId);

    }


    public void setCurrentAppId(String appId) {
        mContext.put(KEY_CURRENT_PROVIDER_APP_ID, appId);

    }

    public void setCurrentProviderId(String tenantId, String appId) {
        mContext.put(KEY_CURRENT_PROVIDER_TENANT_ID, tenantId);
        mContext.put(KEY_CURRENT_PROVIDER_APP_ID, appId);

    }

    public String getCurrentTenantId() {
        return mContext.get(KEY_CURRENT_PROVIDER_TENANT_ID);
    }

    public String getCurrentAppId() {
        return mContext.get(KEY_CURRENT_PROVIDER_APP_ID);
    }

    public Function<Object, String> fetchRedisKey = r -> getCurrentTenantId() + ":" + getCurrentAppId() + ":" + r;


}
