package com.ruoyi.common.security.interceptor;

import com.ruoyi.common.security.context.PreTenantContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Slf4j
@Component
public class AppInterceptor implements HandlerInterceptor {

    @Autowired
    PreTenantContext tenantContext;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {

        String appId = request.getHeader("appId");
        log.info("请求头中appId:{}",appId);
        if (StringUtils.isNotBlank(appId)) {
            tenantContext.setCurrentAppId(appId);
        } else {
            tenantContext.setCurrentAppId("0");
        }
        return true;
    }
}
