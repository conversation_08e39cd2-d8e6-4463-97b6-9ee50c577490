package com.ruoyi.common.security.interceptor;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Slf4j
@Component
public class TenantInterceptor implements HandlerInterceptor {

    /*@Autowired
    PreTenantContext tenantContext;
    @Autowired
    private PreTenantConfigProperties configProperties;*/

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {

     /*   String tenantId = request.getHeader(configProperties.getId());
        if (StringUtils.isNotBlank(tenantId)) {
            tenantContext.setCurrentTenantId(tenantId);
        } else {
            tenantContext.setCurrentTenantId("1");
        }*/
        return true;
    }
}
