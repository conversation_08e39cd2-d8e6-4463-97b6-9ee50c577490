package com.ruoyi.common.security.config;

import com.ruoyi.common.security.interceptor.AppInterceptor;
import com.ruoyi.common.security.interceptor.TenantInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import com.ruoyi.common.security.interceptor.HeaderInterceptor;

/**
 * 拦截器配置
 *
 * <AUTHOR>
 */
public class WebMvcConfig implements WebMvcConfigurer
{
    @Autowired
    TenantInterceptor tenantInterceptor;
    @Autowired
    AppInterceptor appInterceptor;
    /** 不需要拦截地址 */
    public static final String[] excludeUrls = { "/login", "/logout", "/refresh" };
    public static final String[] ADD_PATH = {"/**"};
    public static final String[] EXCLUDE_PATH = {"/code/**"};

    @Override
    public void addInterceptors(InterceptorRegistry registry)
    {
        registry.addInterceptor(getHeaderInterceptor())
                .excludePathPatterns(excludeUrls)
                .addPathPatterns(ADD_PATH)
                .order(-10);

        registry.addInterceptor(tenantInterceptor)
                .excludePathPatterns(EXCLUDE_PATH)
                .addPathPatterns(ADD_PATH)
                .order(10);

        registry.addInterceptor(appInterceptor)
                .excludePathPatterns(EXCLUDE_PATH)
                .addPathPatterns(ADD_PATH)
                .order(20);

        WebMvcConfigurer.super.addInterceptors(registry);
    }

    /**
     * 自定义请求头拦截器
     */
    public HeaderInterceptor getHeaderInterceptor()
    {
        return new HeaderInterceptor();
    }
}
