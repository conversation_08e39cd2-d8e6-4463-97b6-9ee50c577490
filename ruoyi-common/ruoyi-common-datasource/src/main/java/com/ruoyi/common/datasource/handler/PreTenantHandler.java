package com.ruoyi.common.datasource.handler;

import com.baomidou.mybatisplus.extension.plugins.tenant.TenantHandler;
import com.ruoyi.common.datasource.config.PreTenantConfigProperties;
import com.ruoyi.common.security.context.PreTenantContext;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.LongValue;
import net.sf.jsqlparser.expression.NullValue;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


@Slf4j
@Component
public class PreTenantHandler implements TenantHandler {

    @Autowired
    private PreTenantConfigProperties configProperties;
    @Autowired
    private PreTenantContext tentantContext;


    @Override
    public Expression getTenantId(boolean select) {
        //可以通过过滤器从请求中获取对应租户id
        String tenantId = tentantContext.getCurrentTenantId();
        log.info("当前租户为{}", tenantId);
        if (tenantId == null) {
            return new NullValue();
        }
        return new LongValue(tenantId);
    }


    /**
     * 租户字段名
     *
     * @return
     */
    @Override
    public String getTenantIdColumn() {
        //return configProperties.getId();
        return "configProperties.getId()";
    }

    /**
     * 根据表名判断是否进行过滤
     * 忽略掉一些表：如租户表（sys_tenant）本身不需要执行这样的处理
     *
     * @param tableName
     * @return
     */
    @Override
    public boolean doTableFilter(String tableName) {
 /*       //配置是否过滤
        if (!configProperties.isFilter()) {
            return true;
        }
        //黑名单默认不需要过滤
        if (configProperties.getBTables().stream().anyMatch((e) -> e.equalsIgnoreCase(tableName))) {
            return true;
        }
        //白名单的默认全部需要过滤
        if (configProperties.getWTables().stream().anyMatch((e) -> e.equalsIgnoreCase(tableName))) {
            return false;
        }*/
        return true;
    }
}