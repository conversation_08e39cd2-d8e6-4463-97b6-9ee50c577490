package com.ruoyi.common.datasource.config;

import com.baomidou.mybatisplus.core.incrementer.IKeyGenerator;
import com.baomidou.mybatisplus.extension.incrementer.OracleKeyGenerator;
import com.baomidou.mybatisplus.extension.parsers.BlockAttackSqlParser;
import com.baomidou.mybatisplus.core.parser.ISqlParser;
import com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor;
import com.baomidou.mybatisplus.extension.plugins.tenant.TenantSqlParser;
import com.ruoyi.common.datasource.handler.PreAppHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import java.util.ArrayList;
import java.util.List;

/**
 * Mybatis-plus配置
 *
 * <AUTHOR>
 * @date 2022/08/17 14:19
 **/

@EnableTransactionManagement
@Configuration
public class MybatisPlusConfig {

/*    @Autowired
    private PreTenantHandler preTenantHandler;*/

    @Autowired
    private PreAppHandler preAppHandler;

    /**
     * 配置分页
     */
    @Bean
    @Order(0)
    public PaginationInterceptor paginationInterceptor() {

        //封装sql处理器
        List<ISqlParser> sqlParserList = new ArrayList<>();

        // 1攻击 SQL 阻断解析器、加入解析链
        sqlParserList.add(new BlockAttackSqlParser());
        // 2多租户拦截
/*        TenantSqlParser tenantSqlParser = new TenantSqlParser();
        tenantSqlParser.setTenantHandler(preTenantHandler);
        sqlParserList.add(tenantSqlParser);*/
        // 3多应用拦截
        TenantSqlParser appSqlParser = new TenantSqlParser();
        appSqlParser.setTenantHandler(preAppHandler);
        sqlParserList.add(appSqlParser);


        PaginationInterceptor paginationInterceptor = new PaginationInterceptor();
        paginationInterceptor.setSqlParserList(sqlParserList);

        return paginationInterceptor;
    }

    @Bean
    public IKeyGenerator keyGenerator() {
        return new OracleKeyGenerator();
    }

}
