package com.ruoyi.common.datasource.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

@Data
@Component
public class PreTenantConfigProperties {


    @Value("${tenant.config.filter:false}")
    private boolean filter;
    @Value("${tenant.config.id:tenant_id}")
    private String id;
    @Value("${tenant.config.bTables:}")
    private List<String> bTables;
    @Value("${tenant.config.wTables:}")
    private List<String> wTables;

}
