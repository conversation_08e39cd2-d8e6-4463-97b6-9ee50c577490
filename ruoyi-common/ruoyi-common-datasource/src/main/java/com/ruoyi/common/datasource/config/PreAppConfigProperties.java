package com.ruoyi.common.datasource.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

@Data
@Component
public class PreAppConfigProperties {


    @Value("${app.config.filter:true}")
    private boolean filter;

    @Value("${app.config.id:app_id}")
    private String id;

    @Value("${app.config.bTables:}")
    private List<String> bTables;

    @Value("${app.config.wTables:sys_menu,sys_role,sys_user_role}")
    private List<String> wTables;

}
