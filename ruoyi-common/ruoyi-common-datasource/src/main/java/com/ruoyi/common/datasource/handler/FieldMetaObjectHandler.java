

package com.ruoyi.common.datasource.handler;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.model.LoginUser;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 公共字段，自动填充值
 *
 * <AUTHOR>
 */
@Component
public class FieldMetaObjectHandler implements MetaObjectHandler {
    private static final String CREATE_DATE = "createDate";
    private static final String CREATE_DATETIME = "createDatetime";
    private static final String CREATOR = "creator";
    private static final String CREATOR_ID = "creatorId";
    private static final String CREATOR_NAME = "creatorName";
    private static final String UPDATE_DATE = "updateDate";
    private static final String MODIFY_DATETIME = "modifyDatetime";
    private static final String UPDATER = "updater";
    private static final String MODIFER_ID = "modiferId";
    private static final String MODIFER_NAME = "modiferName";
    private static final String DEL_FLAG = "delFlag";
    private static final String DEPT_ID = "deptId";
    private static final String IS_REMOVED = "isRemoved";

    @Override
    public void insertFill(MetaObject metaObject) {
        LoginUser user = SecurityUtils.getLoginUser();

        if (user == null) {
            return;
        }
        Date date = new Date();

        //创建者
        strictInsertFill(metaObject, CREATOR, Long.class, user.getUserid());
        strictInsertFill(metaObject, CREATOR_ID, Integer.class, user.getUserid() == null ? null : user.getUserid().intValue());
        strictInsertFill(metaObject, CREATOR_NAME, String.class, user.getUsername());
        //创建时间
        strictInsertFill(metaObject, CREATE_DATE, Date.class, date);
        strictInsertFill(metaObject, CREATE_DATETIME, Date.class, date);

        //更新者
        strictInsertFill(metaObject, UPDATER, Long.class, user.getUserid());
        //更新时间
        strictInsertFill(metaObject, UPDATE_DATE, Date.class, date);

        //删除标识
        strictInsertFill(metaObject, IS_REMOVED, String.class, "N");
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        LoginUser user = SecurityUtils.getLoginUser();
        if (user == null) {
            return;
        }
        //更新者
        strictUpdateFill(metaObject, UPDATER, Long.class, user.getUserid());
        strictUpdateFill(metaObject, MODIFER_ID, Integer.class, user.getUserid() == null ? null : user.getUserid().intValue());
        strictUpdateFill(metaObject, MODIFER_NAME, String.class, user.getUsername());
        //更新时间
        strictUpdateFill(metaObject, UPDATE_DATE, Date.class, new Date());
        strictUpdateFill(metaObject, MODIFY_DATETIME, Date.class, new Date());
    }
}