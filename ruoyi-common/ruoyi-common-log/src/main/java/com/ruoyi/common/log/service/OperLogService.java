package com.ruoyi.common.log.service;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.system.api.RemoteLogService;
import com.ruoyi.system.api.domain.SysOperLog;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Service
public class OperLogService {
    @Autowired
    private RemoteLogService remoteLogService;

    /**
     * 保存操作日志
     */
    @Async
    public void saveSysLog(SysOperLog sysOperLog)
    {
        remoteLogService.saveOperLog(sysOperLog, SecurityConstants.INNER);
    }
}
