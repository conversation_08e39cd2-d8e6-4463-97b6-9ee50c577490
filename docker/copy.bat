
rem copy sql
echo "begin copy sql "
copy ..\sql\ry_20220613.sql .\mysql\db
copy ..\sql\ry_config_20220510.sql .\mysql\db

rem copy jar
echo "begin copy ruoyi-gateway "
copy ..\ruoyi-gateway\target\ruoyi-gateway.jar .\ruoyi\gateway\jar

echo "begin copy ruoyi-auth "
copy ..\ruoyi-auth\target\ruoyi-auth.jar .\ruoyi\auth\jar

echo "begin copy ruoyi-visual "
copy ..\ruoyi-visual\ruoyi-monitor\target\ruoyi-visual-monitor.jar  .\ruoyi\visual\monitor\jar

echo "begin copy ruoyi-modules-system "
copy ..\ruoyi-modules\ruoyi-system\target\ruoyi-modules-system.jar .\ruoyi\modules\system\jar

echo "begin copy ruoyi-modules-file "
copy ..\ruoyi-modules\ruoyi-file\target\ruoyi-modules-file.jar .\ruoyi\modules\file\jar

echo "begin copy ruoyi-modules-job "
copy ..\ruoyi-modules\ruoyi-job\target\ruoyi-modules-job.jar .\ruoyi\modules\job\jar

echo "begin copy ruoyi-modules-gen "
copy ..\ruoyi-modules\ruoyi-gen\target\ruoyi-modules-gen.jar .\ruoyi\modules\gen\jar

pause
