# Tomcat
server:
  port: 9100

# Spring
spring: 
  application:
    # 应用名称
    name: ruoyi-monitor
  profiles:
    # 环境配置
    active: dev
  cloud:
    nacos:
      discovery:
        server-addr: 172.22.0.77:8848
        # 命名空间
        namespace: ecs-dev
      config:
        server-addr: 172.22.0.77:8848
        file-extension: yml
        # 命名空间
        namespace: ecs-dev
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
