package com.ruoyi.file.service;

import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.utils.file.FileTypeUtils;
import com.ruoyi.common.core.utils.uuid.Seq;
import com.ruoyi.file.config.MinioConfig;
import com.ruoyi.file.utils.FileUploadUtils;
import io.minio.MinioClient;
import io.minio.PutObjectArgs;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Minio 文件存储
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class MinioSysFileServiceImpl implements ISysFileService {
    @Autowired
    private MinioConfig minioConfig;

    @Autowired
    private MinioClient client;


    /**
     * 本地文件上传接口
     *
     * @param file 上传的文件
     * @return 访问地址
     * @throws Exception
     */
    @Override
    public String uploadFileFolder(MultipartFile file, String folderName) {
        String fileName = "/" + folderName + "/" + FileUploadUtils.extractFilename(file);
        PutObjectArgs args = null;
        log.info("开始上传");
        try {
            args = PutObjectArgs.builder()
                    .bucket(minioConfig.getBucketName())
                    .object(fileName)
                    .stream(file.getInputStream(), file.getSize(), -1)
                    .contentType(file.getContentType())
                    .build();
        } catch (IOException e) {
            log.info(e.getMessage(), "封装错误");
        }
        try {
            client.putObject(args);
        } catch (Exception e) {
            log.info(e.getMessage(), "上传错误");
        }
        return minioConfig.getUrl() + "/" + minioConfig.getBucketName() + fileName;
    }


    /**
     * 本地文件上传接口
     *
     * @param file 上传的文件
     * @return 访问地址
     * @throws Exception
     */
    @Override
    public String uploadFile(MultipartFile file) throws Exception {
        String fileName = FileUploadUtils.extractFilename(file);
        PutObjectArgs args = PutObjectArgs.builder()
                .bucket(minioConfig.getBucketName())
                .object(fileName)
                .stream(file.getInputStream(), file.getSize(), -1)
                .contentType(file.getContentType())
                .build();
        client.putObject(args);
        return minioConfig.getUrl() + "/" + minioConfig.getBucketName() + "/" + fileName;
    }

    @Override
    public String mediaUpload(MultipartFile file, String folderName, String title) {
        String fileName;
        if (title != null) {
            fileName = folderName + "/" + StringUtils.format("{}/{}/{}_{}.{}", DateUtils.datePath(), title,
                    FilenameUtils.getBaseName(file.getOriginalFilename()), Seq.getNSeq(new AtomicInteger(1), 3), FileTypeUtils.getExtension(file));
        } else {
            fileName = folderName + "/" + StringUtils.format("{}/{}_{}.{}", DateUtils.datePath(),
                    FilenameUtils.getBaseName(file.getOriginalFilename()), Seq.getNSeq(new AtomicInteger(1), 3), FileTypeUtils.getExtension(file));
        }
        PutObjectArgs args = null;
        log.info("开始上传");
        try {
            args = PutObjectArgs.builder()
                    .bucket(minioConfig.getMediaBucketName())
                    .object(fileName)
                    .stream(file.getInputStream(), file.getSize(), -1)
                    .contentType(file.getContentType())
                    .build();
        } catch (IOException e) {
            log.info(e.getMessage(), "封装错误");
        }
        try {
            client.putObject(args);
        } catch (Exception e) {
            log.info(e.getMessage(), "上传错误");
        }
        return minioConfig.getUrl() + "/" + minioConfig.getMediaBucketName() + "/" + fileName;
    }

}
