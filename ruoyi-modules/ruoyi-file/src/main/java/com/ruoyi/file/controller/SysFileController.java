package com.ruoyi.file.controller;

import com.ruoyi.file.contant.FolderType;
import com.ruoyi.file.entity.Message;
import com.ruoyi.file.entity.Template;
import com.ruoyi.file.service.MinioSysFileServiceImpl;
import com.ruoyi.file.utils.ExcelUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.file.FileUtils;
import com.ruoyi.file.service.ISysFileService;
import com.ruoyi.system.api.domain.SysFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * 文件请求处理
 *
 * <AUTHOR>
 */

@RestController
public class SysFileController
{
    private static final Logger log = LoggerFactory.getLogger(SysFileController.class);

    @Autowired
    private ISysFileService sysFileService;

    @Autowired
    private MinioSysFileServiceImpl minioSysFileService;

    /**
     * 文件上传请求
     */
    @PostMapping("upload")
    public R<SysFile> upload(MultipartFile file)
    {
        try
        {
            // 上传并返回访问地址
            String url = sysFileService.uploadFile(file);
            SysFile sysFile = new SysFile();
            sysFile.setName(FileUtils.getName(url));
            sysFile.setUrl(url);
            return R.ok(sysFile);
        }
        catch (Exception e)
        {
            log.error("上传文件失败", e);
            return R.fail(e.getMessage());
        }
    }



    /**
     * 模板上传请求
     */
    @PostMapping("uploadTemplate")
    public R<Template> uploadTemplate(MultipartFile file)
    {
        try {
            String url = minioSysFileService.uploadFileFolder(file, FolderType.NOMODEL);
            Template template = new Template();
            template.setFileName(file.getOriginalFilename());
            template.setFileUrl(url);
            String fileType = file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf("."));
            template.setFileType(fileType);
            return R.ok(template);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }
    /**
     * 模板列表上传请求
     */
    @PostMapping("uploadTemplateList")
    public R<List<Template>> uploadTemplateList(MultipartFile[] files)
    {
        try {
            List<Template> list = new ArrayList<>();
            for (MultipartFile file : files) {
                String url = minioSysFileService.uploadFile(file);
                Template template = new Template();
                template.setFileName(file.getName());
                template.setFileUrl(url);
                String fileType = file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf("."));
                template.setFileType(fileType);
                list.add(template);
            }
            return R.ok(list);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }




    /**
     * 模板下载
     */
    @PostMapping("/getTemplate")
    public void export( HttpServletResponse response) {
        String fileName = "自定义模板";
        String sheetName = "自定义模板";
        List<Message> templateList = new ArrayList<>();
        templateList.add(new Message());
        try {
            ExcelUtil.writeExcel(response, templateList, fileName, sheetName, Message.class);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }



    /**
     * 文件上传请求
     */
    @PostMapping("uploadList")
    public R<List<SysFile>> uploadList(MultipartFile[] files)
    {
        try
        {
            List<SysFile> list = new ArrayList<>();
            if (files != null){
                for (MultipartFile file : files) {
                    // 上传并返回访问地址
                    String url = sysFileService.uploadFile(file);
                    SysFile sysFile = new SysFile();
                    sysFile.setName(FileUtils.getName(url));
                    sysFile.setUrl(url);
                    list.add(sysFile);
                }
            }

            return R.ok(list);
        }
        catch (Exception e)
        {
            log.error("上传文件失败", e);
            return R.fail(e.getMessage());
        }
    }

    /**
     * 吉宁文件上传请求
     */
    @PostMapping("mediaUpload")
    public R<Template> mediaUpload(MultipartFile file,String folderName,String title)
    {
        try {
            String url = minioSysFileService.mediaUpload(file,folderName,title);
            Template template = new Template();
            template.setFileName(file.getOriginalFilename());
            template.setFileUrl(url);
            String fileType = file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf("."));
            template.setFileType(fileType);
            return R.ok(template);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

}