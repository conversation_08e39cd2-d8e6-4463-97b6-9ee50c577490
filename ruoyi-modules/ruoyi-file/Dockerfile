# 构建镜像，执行命令：【docker build -t call-coupon:2.0 .】
FROM harbor.hoair.cn/library/openjdk:8-jdk
MAINTAINER Arber C.

# 时区问题
RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime
RUN echo 'Asia/Shanghai' >/etc/timezone

# 字体
#COPY fonts/simsun.ttc /usr/share/fonts/simsun.ttc

ADD ruoyi-modules/ruoyi-file/target/ruoyi-modules-file.jar /app/app.jar

ENTRYPOINT ["java", "-server", "-Xms512M", "-Xmx2048M",  "-Dfile.encoding=UTF-8", "-XX:+HeapDumpOnOutOfMemoryError", "-jar", "/app/app.jar" ]


