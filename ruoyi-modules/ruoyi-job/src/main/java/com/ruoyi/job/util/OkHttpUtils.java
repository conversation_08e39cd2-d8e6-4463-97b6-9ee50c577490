package com.ruoyi.job.util;
 

import java.io.IOException;
import java.util.concurrent.TimeUnit;

import lombok.extern.slf4j.Slf4j;
import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

import static org.bouncycastle.cms.CMSSignedGenerator.DATA;


@Slf4j
public class OkHttpUtils {
    /**
     * HTTP接口-GET方式，请求参数形式为params形式
     *
     * @param url
     * @return String
     */
    public static String sendGet(String url) throws IOException {
        OkHttpClient client = new OkHttpClient().newBuilder()
                .connectTimeout(180, TimeUnit.SECONDS)
                .readTimeout(180, TimeUnit.SECONDS)
                .writeTimeout(180, TimeUnit.SECONDS)
                .build();

        Request request = new Request.Builder()
                .url(url)
                .get()
                .build();
        Response response;
        String result;
        try {
            response = client.newCall(request).execute();
            result = response.body().string();
        } catch (IOException e) {
            throw new IOException(DATA, e);
        }
        return result;
    }

 

}
 