package com.ruoyi.job.util;

import com.alibaba.cloud.nacos.NacosDiscoveryProperties;
import com.alibaba.nacos.api.NacosFactory;
import com.alibaba.nacos.api.naming.NamingService;
import com.alibaba.nacos.api.naming.pojo.Instance;
import org.springframework.stereotype.Service;
import java.util.List;

@Service
public class FeignServiceDiscovery {

    private final NacosDiscoveryProperties nacosDiscoveryProperties;

    public FeignServiceDiscovery(NacosDiscoveryProperties nacosDiscoveryProperties) {
        this.nacosDiscoveryProperties = nacosDiscoveryProperties;
    }

    public List<Instance> getFeignServices(String feignName) {
        try {
            NamingService namingService = NacosFactory.createNamingService(nacosDiscoveryProperties.getNacosProperties());
            return namingService.getAllInstances(feignName);
        } catch (Exception e) {
            e.printStackTrace();
            // 处理异常情况
            return null;
        }
    }
}
