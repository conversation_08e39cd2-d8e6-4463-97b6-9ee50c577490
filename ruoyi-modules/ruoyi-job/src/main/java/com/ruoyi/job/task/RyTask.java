package com.ruoyi.job.task;

import com.alibaba.nacos.api.naming.pojo.Instance;
import com.ruoyi.job.util.FeignServiceDiscovery;
import com.ruoyi.job.util.OkHttpUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.ruoyi.common.core.utils.StringUtils;

import java.io.IOException;
import java.util.List;

/**
 * 定时任务调度测试
 * 
 * <AUTHOR>
 */
@Component("ryTask")
public class RyTask
{

    @Autowired
    private FeignServiceDiscovery serviceDiscovery;

    public void ryMultipleParams(String feignName, String baseUrl, Long l, Double d, Integer i) throws IOException {
        List<Instance> feignServices = serviceDiscovery.getFeignServices(feignName);
        String url = "http://"+feignServices.get(0).getIp()+":"+feignServices.get(0).getPort()+ baseUrl;
        OkHttpUtils.sendGet(url);
        System.out.println(feignServices.get(0));
    }

    public void ryParams(String params)
    {
        System.out.println("执行有参方法：" + params);
    }

    public void ryNoParams()
    {

        System.out.println("执行无参方法");
    }
}
