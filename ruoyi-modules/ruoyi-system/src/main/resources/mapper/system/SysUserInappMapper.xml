<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SysUserInappMapper">
    
    <resultMap type="SysUserInapp" id="SysUserInappResult">
        <result property="userInappId"    column="user_inapp_id"    />
        <result property="userId"    column="user_id"    />
        <result property="appId"    column="app_id"    />
    </resultMap>

    <sql id="selectSysUserInappVo">
        select user_inapp_id, user_id, app_id from sys_user_inapp
    </sql>

    <select id="selectSysUserInappList" parameterType="SysUserInapp" resultMap="SysUserInappResult">
        <include refid="selectSysUserInappVo"/>
        <where>  
            <if test="userInappId != null "> and user_inapp_id = #{userInappId}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="appId != null "> and app_id = #{appId}</if>
        </where>
    </select>
    
    <select id="selectSysUserInappByUserInappId" parameterType="Long" resultMap="SysUserInappResult">
        <include refid="selectSysUserInappVo"/>
        where user_inapp_id = #{userInappId}
    </select>
        
    <insert id="insertSysUserInapp" parameterType="SysUserInapp">
        insert into sys_user_inapp
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userInappId != null">user_inapp_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="appId != null">app_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userInappId != null">#{userInappId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="appId != null">#{appId},</if>
         </trim>
    </insert>

    <update id="updateSysUserInapp" parameterType="SysUserInapp">
        update sys_user_inapp
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="appId != null">app_id = #{appId},</if>
        </trim>
        where user_inapp_id = #{userInappId}
    </update>

    <delete id="deleteSysUserInappByUserInappId" parameterType="Long">
        delete from sys_user_inapp where user_inapp_id = #{userInappId}
    </delete>

    <delete id="deleteSysUserInappByUserInappIds" parameterType="String">
        delete from sys_user_inapp where user_inapp_id in 
        <foreach item="userInappId" collection="array" open="(" separator="," close=")">
            #{userInappId}
        </foreach>
    </delete>
</mapper>