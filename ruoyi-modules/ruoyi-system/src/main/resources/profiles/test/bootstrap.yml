# Tomcat
server:
  port: 9201

# Spring
spring: 
  application:
    # 应用名称
    name: ruoyi-system
  #profiles:
    # 环境配置
    #active: test
  cloud:
    nacos:
      discovery:
        server-addr: 172.22.0.77:8848
        # 命名空间
        namespace: ecs-test
        username: ecscloud
        password: 2FRFTfRk
      config:
        server-addr: 172.22.0.77:8848
        file-extension: yml
        # 命名空间
        namespace: ecs-test
        username: ecscloud
        password: 2FRFTfRk
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
