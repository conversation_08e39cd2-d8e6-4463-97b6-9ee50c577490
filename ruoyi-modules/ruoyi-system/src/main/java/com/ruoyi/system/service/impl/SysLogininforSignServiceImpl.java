package com.ruoyi.system.service.impl;

import com.ruoyi.system.api.domain.SysLogininfor;
import com.ruoyi.system.domain.dto.SacSignaturesReqDto;
import com.ruoyi.system.domain.dto.VerifySignatureReqDto;
import com.ruoyi.system.service.ISacSignaturesService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;

/**
 * 登录日志签名服务实现类
 * 处理sys_logininfor表的签名和验签
 */
@Slf4j
@Service
public class SysLogininforSignServiceImpl {
    
    @Autowired
    private ISacSignaturesService sacSignaturesService;
    
    // 登录日志表常量
    private static final String TABLE_NAME = "sys_logininfor";
    private static final String TABLE_FIELD = "user_name,status,ipaddr,msg,access_time";
    
    /**
     * 为登录日志记录创建签名
     *
     * @param logininfor 登录日志对象
     * @return 签名操作结果
     */
    public int signLogininfor(SysLogininfor logininfor) {
        try {
            log.info("开始登录日志签名 - 日志ID: {}, 用户名: {}", logininfor.getInfoId(), logininfor.getUserName());

            // 构建待签名数据（直接拼接所有字段值）
            String dataToSign = buildLogininforSignData(logininfor);

            // 记录签名原文详细信息
            log.info("登录日志签名原文 - 日志ID: {}, 用户名: {}, 数据长度: {}, 原文内容: [{}]",
                logininfor.getInfoId(), logininfor.getUserName(),
                dataToSign != null ? dataToSign.length() : 0,
                dataToSign != null ? dataToSign : "null");

            // 构建签名请求
            SacSignaturesReqDto reqDto = new SacSignaturesReqDto();
            reqDto.setTableName(TABLE_NAME);
            reqDto.setTableField(TABLE_FIELD);
            reqDto.setRecordId(String.valueOf(logininfor.getInfoId()));
            reqDto.setDataToSign(dataToSign);

            log.info("登录日志签名请求 - 表名: {}, 字段: {}, 记录ID: {}",
                TABLE_NAME, TABLE_FIELD, logininfor.getInfoId());

            // 执行签名
            int result = sacSignaturesService.updateSignatures(reqDto);

            if (result > 0) {
                log.info("登录日志签名成功 - 日志ID: {}, 用户名: {}, 操作结果: {}",
                    logininfor.getInfoId(), logininfor.getUserName(), result);
            } else {
                log.info("登录日志签名失败 - 日志ID: {}, 用户名: {}, 操作结果: {}",
                    logininfor.getInfoId(), logininfor.getUserName(), result);
            }

            return result;
        } catch (Exception e) {
            log.error("登录日志签名异常 - 日志ID: {}, 用户名: {}, 错误: {}",
                logininfor.getInfoId(), logininfor.getUserName(), e.getMessage(), e);
            throw new RuntimeException("登录日志签名失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 验证登录日志记录的签名
     * 
     * @param logininfor 登录日志对象
     * @return 验证结果
     */
    public boolean verifyLogininfor(SysLogininfor logininfor) {
        try {
            // 构建待验证数据
            String dataToVerify = buildLogininforSignData(logininfor);
            
            // 构建验证请求
            VerifySignatureReqDto reqDto = new VerifySignatureReqDto();
            reqDto.setTableName(TABLE_NAME);
            reqDto.setTableField(TABLE_FIELD);
            reqDto.setRecordId(String.valueOf(logininfor.getInfoId()));
            reqDto.setDataToVerify(dataToVerify);
            
            // 执行验证
            boolean result = sacSignaturesService.verifySignature(reqDto);
            
            if (result) {
                log.debug("登录日志签名验证通过，日志ID：{}，用户名：{}", logininfor.getInfoId(), logininfor.getUserName());
            } else {
                log.warn("登录日志签名验证失败，日志ID：{}，用户名：{}", logininfor.getInfoId(), logininfor.getUserName());
            }
            
            return result;
        } catch (Exception e) {
            log.error("登录日志签名验证异常，日志ID：{}，用户名：{}，错误：{}", 
                logininfor.getInfoId(), logininfor.getUserName(), e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 构建登录日志的签名数据
     * 直接拼接所有字段值：user_name + status + ipaddr + msg + access_time
     *
     * @param logininfor 登录日志对象
     * @return 待签名的数据字符串
     */
    private String buildLogininforSignData(SysLogininfor logininfor) {
        StringBuilder sb = new StringBuilder();

        // 用户名
        sb.append(logininfor.getUserName() != null ? logininfor.getUserName() : "");

        // 状态
        sb.append(logininfor.getStatus() != null ? logininfor.getStatus() : "");

        // IP地址
        sb.append(logininfor.getIpaddr() != null ? logininfor.getIpaddr() : "");

        // 消息
        sb.append(logininfor.getMsg() != null ? logininfor.getMsg() : "");

        // 访问时间（格式化为字符串确保一致性）
        if (logininfor.getAccessTime() != null) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            sb.append(sdf.format(logininfor.getAccessTime()));
        }

        return sb.toString();
    }
}
