package com.ruoyi.system.domain;

import java.util.Date;

/**
 * 密评签名表
 */
public class SacSignatures {
    /**
     * 主键
     */
    private Long id;

    /**
     * 被签名数据所在的表
     */
    private String tableName;

    /**
     * 被签名的数据字段（或字段集合）
     */
    private String tableField;

    /**
     * 对应数据记录主键
     */
    private String recordId;

    /**
     * 签名值
     */
    private String signValue;

    /**
     * 签名时间
     */
    private Date createTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public String getTableField() {
        return tableField;
    }

    public void setTableField(String tableField) {
        this.tableField = tableField;
    }

    public String getRecordId() {
        return recordId;
    }

    public void setRecordId(String recordId) {
        this.recordId = recordId;
    }

    public String getSignValue() {
        return signValue;
    }

    public void setSignValue(String signValue) {
        this.signValue = signValue;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}