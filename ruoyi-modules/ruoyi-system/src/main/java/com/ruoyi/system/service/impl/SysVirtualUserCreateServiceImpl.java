package com.ruoyi.system.service.impl;

import com.ruoyi.common.security.context.PreTenantContext;
import com.ruoyi.system.api.domain.SysUser;
import com.ruoyi.system.domain.SysUserRole;
import com.ruoyi.system.mapper.SysUserMapper;
import com.ruoyi.system.mapper.SysUserRoleMapper;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 用户创建服务实现类
 * 提供批量创建用户的功能，用于密评任务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class SysVirtualUserCreateServiceImpl {

    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private SysUserRoleMapper sysUserRoleMapper;

    @Autowired
    private SysPasswordCryptoService sysPasswordCryptoService;

    @Autowired
    private SysUserNameCryptoService sysUserNameCryptoService;

    @Autowired
    private SysUserSignServiceImpl sysUserSignService;

    @Autowired
    private SysPermissionSignServiceImpl sysPermissionSignService;

    @Autowired(required = false)
    private PreTenantContext preTenantContext;

    /** 用户默认角色ID（普通角色） */
    private static final Long DEFAULT_ROLE_ID = 2L;

    /**
     * 批量创建用户
     *
     * @param userCount 创建用户数量
     * @param userPrefix 用户名前缀
     * @param defaultPassword 默认密码
     * @return 创建结果统计
     */
    @Transactional(rollbackFor = Exception.class)
    public VirtualUserCreateResult createUsers(int userCount, String userPrefix, String defaultPassword) {
        log.info("开始批量创建用户，数量: {}, 前缀: {}, 默认密码: {}", userCount, userPrefix, "***");

        // 设置app_id上下文，解决PreAppHandler多租户拦截问题
        if (preTenantContext != null) {
            preTenantContext.setCurrentAppId("2");
        }

        VirtualUserCreateResult result = new VirtualUserCreateResult();
        result.totalCount = userCount;

        try {
            // 对默认密码进行BCrypt加密
            BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
            String bcryptPassword = passwordEncoder.encode(defaultPassword);
            log.info("默认密码BCrypt加密完成");

            // 对BCrypt密码进行SAC加密
            String encryptedPassword = sysPasswordCryptoService.encryptPassword(bcryptPassword);
            log.info("默认密码SAC加密完成");

            for (int i = 1; i <= userCount; i++) {
                try {
                    // 生成原始用户名
                    String originalUserName = userPrefix + String.format("%03d", i);
                    
                    // 检查用户名是否已存在
                    if (sysUserMapper.checkUserNameUnique(originalUserName) > 0) {
                        result.skippedCount++;
                        log.warn("用户名已存在，跳过创建: {}", originalUserName);
                        continue;
                    }

                    // 对用户名进行加密
                    String encryptedUserName = sysUserNameCryptoService.encryptUserName(originalUserName);
                    
                    // 再次检查加密后的用户名是否已存在
                    if (sysUserMapper.checkUserNameUnique(encryptedUserName) > 0) {
                        result.skippedCount++;
                        log.warn("加密后用户名已存在，跳过创建: {}", originalUserName);
                        continue;
                    }

                    // 创建虚拟用户对象
                    SysUser virtualUser = createVirtualUserObject(encryptedUserName, encryptedPassword, i);

                    // 插入用户
                    int insertResult = sysUserMapper.insertUser(virtualUser);

                    if (insertResult > 0) {
                        // 为用户分配默认角色
                        try {
                            insertUserRole(virtualUser.getUserId());
                        } catch (Exception e) {
                            log.warn("用户角色分配失败，用户ID：{}，原始用户名：{}，错误：{}",
                                virtualUser.getUserId(), originalUserName, e.getMessage());
                        }

                        // 对用户关键字段进行签名（使用加密后的用户名和密码）
                        try {
                            sysUserSignService.signUserData(
                                virtualUser.getUserId(),
                                encryptedUserName,
                                encryptedPassword,
                                virtualUser.getPhonenumber()
                            );
                        } catch (Exception e) {
                            log.warn("用户签名失败，用户ID：{}，原始用户名：{}，错误：{}",
                                virtualUser.getUserId(), originalUserName, e.getMessage());
                        }

                        result.successCount++;
                        log.info("用户创建成功 - 原始用户名: {}, 用户ID: {}", originalUserName, virtualUser.getUserId());
                    } else {
                        result.failCount++;
                        log.error("用户创建失败，数据库插入失败 - 原始用户名: {}", originalUserName);
                    }

                } catch (Exception e) {
                    result.failCount++;
                    log.error("用户创建失败，原始用户名：{}，错误：{}", userPrefix + String.format("%03d", i), e.getMessage(), e);
                }

                // 每处理50个用户输出一次进度
                if ((result.successCount + result.failCount + result.skippedCount) % 50 == 0) {
                    log.info("用户创建进度 - 总数: {}, 成功: {}, 失败: {}, 跳过: {}",
                        result.totalCount, result.successCount, result.failCount, result.skippedCount);
                }
            }

            log.info("用户批量创建完成 - 总数: {}, 成功: {}, 失败: {}, 跳过: {}",
                result.totalCount, result.successCount, result.failCount, result.skippedCount);
            
        } catch (Exception e) {
            log.error("用户批量创建异常：{}", e.getMessage(), e);
            result.errorMessage = e.getMessage();
        }

        return result;
    }

    /**
     * 创建用户对象
     *
     * @param encryptedUserName 加密后的用户名
     * @param encryptedPassword 加密后的密码
     * @param index 用户序号
     * @return 用户对象
     */
    private SysUser createVirtualUserObject(String encryptedUserName, String encryptedPassword, int index) {
        SysUser user = new SysUser();
        
        // 基本信息
        user.setUserName(encryptedUserName);
        user.setPassword(encryptedPassword);
        user.setNickName("用户" + String.format("%03d", index));
        user.setEmail("juneyao" + String.format("%03d", index) + "@juneyaoair.com");
        user.setPhonenumber("1380000" + String.format("%04d", index));
        user.setSex("0"); // 男
        user.setStatus("0"); // 正常
        user.setDelFlag("0"); // 未删除

        // 设置部门ID（可以设置为一个默认部门，这里设置为103）
        user.setDeptId(103L);

        // 设置创建信息
        user.setCreateBy("system");
        user.setCreateTime(new Date());
        user.setRemark("密评用户");
        
        return user;
    }

    /**
     * 为用户分配默认角色
     *
     * @param userId 用户ID
     */
    private void insertUserRole(Long userId) {
        // 创建用户角色关联
        SysUserRole userRole = new SysUserRole();
        userRole.setUserId(userId);
        userRole.setRoleId(DEFAULT_ROLE_ID);

        List<SysUserRole> userRoleList = new ArrayList<>();
        userRoleList.add(userRole);

        // 批量插入用户角色关联
        sysUserRoleMapper.batchUserRole(userRoleList);

        // 对用户角色关联进行签名（仅针对营销后台app_id=2的用户）
        try {
            // 设置app_id上下文
            if (preTenantContext != null) {
                preTenantContext.setCurrentAppId("2");
            }

            // 查询该用户在app_id=2下的角色
            List<Long> marketingRoleIds = sysUserRoleMapper.selectRoleListBySac(userId, 2L);
            if (!marketingRoleIds.isEmpty()) {
                sysPermissionSignService.signUserRoles(userId, marketingRoleIds);
                log.info("用户角色关联签名成功，用户ID：{}，角色数量：{}", userId, marketingRoleIds.size());
            }
        } catch (Exception e) {
            log.warn("用户角色关联签名失败，用户ID：{}，错误：{}", userId, e.getMessage());
        }
    }

    /**
     * 用户创建结果统计
     */
    @Data
    public static class VirtualUserCreateResult {
        /** 总用户数 */
        public int totalCount = 0;
        /** 成功创建数 */
        public int successCount = 0;
        /** 失败数 */
        public int failCount = 0;
        /** 跳过数（用户名已存在） */
        public int skippedCount = 0;
        /** 错误信息 */
        public String errorMessage;
    }
}
