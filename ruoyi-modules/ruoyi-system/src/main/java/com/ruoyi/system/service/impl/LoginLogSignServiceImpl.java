package com.ruoyi.system.service.impl;

import com.haitai.response.ResultCode;
import com.ruoyi.system.api.domain.SysLogininfor;
import com.ruoyi.sac.service.SignService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;

/**
 * 登录日志签名服务类
 *
 * 
 * <AUTHOR>
@Service
public class LoginLogSignServiceImpl {
    
    private static final Logger log = LoggerFactory.getLogger(LoginLogSignServiceImpl.class);
    
    @Autowired(required = false)
    private SignService signService;
    
    /**
     * 对登录日志进行签名
     * 签名字段包括：用户名、IP地址、登录状态、消息、访问时间
     * 
     * @param logininfor 登录日志对象
     * @return 签名后的登录日志对象
     */
    public SysLogininfor signLoginLog(SysLogininfor logininfor) {
        if (signService == null) {
            log.warn("签名服务未初始化，跳过登录日志签名");
            return logininfor;
        }
        
        try {
            // 构建待签名数据
            String dataToSign = buildSignData(logininfor);
            
            // 进行签名
            com.haitai.response.ResultCode<String> signResult = signService.signData(dataToSign);
            
            if (signResult.getCode() == 0 && signResult.getData() != null) {
//                logininfor.setLogSignature(signResult.getData());
                log.debug("登录日志签名成功，用户：{}", logininfor.getUserName());
            } else {
                log.error("登录日志签名失败，用户：{}，错误：{}", logininfor.getUserName(), signResult.getMessage());
            }
            
        } catch (Exception e) {
            log.error("登录日志签名异常，用户：{}，异常：{}", logininfor.getUserName(), e.getMessage());
        }
        
        return logininfor;
    }
    
    /**
     * 验证登录日志签名
     * 
     * @param logininfor 登录日志对象
     * @return 验证结果
     */
    public boolean verifyLoginLogSignature(SysLogininfor logininfor) {
        if (signService == null) {
            log.warn("签名服务未初始化，跳过登录日志验签");
            return true;
        }
        
//        if (logininfor.getLogSignature() == null || logininfor.getLogSignature().isEmpty()) {
//            log.warn("登录日志未签名，用户：{}", logininfor.getUserName());
//            return true; // 未签名的日志暂时认为验证通过
//        }
        
        try {
            // 构建待验证数据
            String dataToVerify = buildSignData(logininfor);
            
            // 进行验签
            com.haitai.response.ResultCode<Boolean> verifyResult = new ResultCode<>();
            // TODO
//                    signService.verifySignedData(dataToVerify, logininfor.getLogSignature());
            
            if (verifyResult.getCode() == 0) {
                boolean isValid = Boolean.TRUE.equals(verifyResult.getData());
                if (!isValid) {
                    log.error("登录日志签名验证失败，用户：{}，日志ID：{}", logininfor.getUserName(), logininfor.getInfoId());
                }
                return isValid;
            } else {
                log.error("登录日志签名验证异常，用户：{}，错误：{}", logininfor.getUserName(), verifyResult.getMessage());
                return false;
            }
            
        } catch (Exception e) {
            log.error("登录日志签名验证异常，用户：{}，异常：{}", logininfor.getUserName(), e.getMessage());
            return false;
        }
    }
    
    /**
     * 构建待签名/验证的数据
     * 将登录日志的关键字段按固定格式拼接
     * 
     * @param logininfor 登录日志对象
     * @return 待签名的数据字符串
     */
    private String buildSignData(SysLogininfor logininfor) {
        StringBuilder sb = new StringBuilder();
        
        // 用户名
        sb.append("userName:").append(logininfor.getUserName() != null ? logininfor.getUserName() : "");
        sb.append("|");
        
        // 用户ID
        sb.append("userID:").append(logininfor.getUserID() != null ? logininfor.getUserID() : "");
        sb.append("|");
        
        // 应用ID
        sb.append("appID:").append(logininfor.getAppID() != null ? logininfor.getAppID() : "");
        sb.append("|");
        
        // IP地址
        sb.append("ipaddr:").append(logininfor.getIpaddr() != null ? logininfor.getIpaddr() : "");
        sb.append("|");
        
        // 登录状态
        sb.append("status:").append(logininfor.getStatus() != null ? logininfor.getStatus() : "");
        sb.append("|");
        
        // 消息
        sb.append("msg:").append(logininfor.getMsg() != null ? logininfor.getMsg() : "");
        sb.append("|");
        
        // 访问时间
        if (logininfor.getAccessTime() != null) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            sb.append("accessTime:").append(sdf.format(logininfor.getAccessTime()));
        } else {
            sb.append("accessTime:");
        }
        
        String result = sb.toString();
        log.debug("构建登录日志签名数据：{}", result);
        return result;
    }

}
