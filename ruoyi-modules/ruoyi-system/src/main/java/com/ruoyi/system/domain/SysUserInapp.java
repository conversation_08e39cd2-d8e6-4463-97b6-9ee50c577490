package com.ruoyi.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 用户应用租户对象 sys_user_inapp
 * 
 * <AUTHOR>
 * @date 2024-02-01
 */
public class SysUserInapp extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 用户应用租户ID */
    @Excel(name = "用户应用租户ID")
    private Long userInappId;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 应用租户ID	 */
    @Excel(name = "应用租户ID	")
    private Long appId;

    public void setUserInappId(Long userInappId) 
    {
        this.userInappId = userInappId;
    }

    public Long getUserInappId() 
    {
        return userInappId;
    }
    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }
    public void setAppId(Long appId) 
    {
        this.appId = appId;
    }

    public Long getAppId() 
    {
        return appId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("userInappId", getUserInappId())
            .append("userId", getUserId())
            .append("appId", getAppId())
            .toString();
    }
}
