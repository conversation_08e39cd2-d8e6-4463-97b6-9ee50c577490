package com.ruoyi.system.controller;

import com.ruoyi.system.domain.dto.SacSignaturesReqDto;
import com.ruoyi.system.domain.dto.VerifySignatureReqDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.domain.SacSignatures;
import com.ruoyi.system.service.ISacSignaturesService;

/**
 * 密评签名控制器
 */
@RestController
@RequestMapping("/sac/signatures")
public class SacSignaturesController extends BaseController {
    
    @Autowired
    private ISacSignaturesService sacSignaturesService;

    /**
     * 数据签名
     */
    @PostMapping("/update")
    public AjaxResult updateSignatures(@RequestBody SacSignaturesReqDto reqDto) {
        return toAjax(sacSignaturesService.updateSignatures(reqDto));
    }

    
    /**
     * 验证签名
     */
    @PostMapping("/verify")
    public AjaxResult verifySignature(@RequestBody VerifySignatureReqDto reqDto) {
        boolean result = sacSignaturesService.verifySignature(reqDto);
        return AjaxResult.success(result);
    }
} 