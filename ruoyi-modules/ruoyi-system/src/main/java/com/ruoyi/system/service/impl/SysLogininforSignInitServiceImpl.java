package com.ruoyi.system.service.impl;

import com.ruoyi.system.api.domain.SysLogininfor;
import com.ruoyi.system.mapper.SysLogininforMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 登录日志签名初始化服务实现类
 * 专门处理登录日志的批量签名初始化
 */
@Slf4j
@Service
public class SysLogininforSignInitServiceImpl {
    
    @Autowired
    private SysLogininforMapper sysLogininforMapper;
    
    @Autowired
    private SysLogininforSignServiceImpl sysLogininforSignService;
    
    /**
     * 初始化登录日志表签名
     * 
     * @param limit 每批处理的数量限制，0表示处理全部
     * @return 初始化结果
     */
    public LogininforSignInitResult initLogininforSignatures(int limit) {
        log.info("开始初始化登录日志表签名，限制数量：{}", limit == 0 ? "全部" : limit);
        
        LogininforSignInitResult result = new LogininforSignInitResult();
        
        try {
            // 查询登录日志
            SysLogininfor queryParam = new SysLogininfor();
            List<SysLogininfor> logininforList = sysLogininforMapper.selectLogininforList(queryParam);
            
            result.totalCount = logininforList.size();
            log.info("找到 {} 条登录日志记录", result.totalCount);
            
            if (result.totalCount == 0) {
                log.warn("未找到登录日志数据");
                return result;
            }
            
            // 如果设置了限制，只处理指定数量
            List<SysLogininfor> processList = logininforList;
            if (limit > 0 && logininforList.size() > limit) {
                processList = logininforList.subList(0, limit);
                log.info("限制处理数量为 {} 条", limit);
            }
            
            result.processCount = processList.size();
            
            // 批量处理签名
            for (SysLogininfor logininfor : processList) {
                try {
                    sysLogininforSignService.signLogininfor(logininfor);
                    result.successCount++;
                    
                    // 每处理100条记录输出一次进度
                    if (result.successCount % 100 == 0) {
                        log.info("已处理 {} 条登录日志签名", result.successCount);
                    }
                } catch (Exception e) {
                    result.failCount++;
                    log.error("登录日志签名失败，日志ID：{}，用户名：{}，错误：{}", 
                        logininfor.getInfoId(), logininfor.getUserName(), e.getMessage());
                }
            }
            
            log.info("登录日志表签名初始化完成 - 总数: {}, 处理: {}, 成功: {}, 失败: {}", 
                result.totalCount, result.processCount, result.successCount, result.failCount);
            
        } catch (Exception e) {
            log.error("登录日志表签名初始化异常：{}", e.getMessage(), e);
            result.errorMessage = e.getMessage();
        }
        
        return result;
    }
    
    /**
     * 分页初始化登录日志签名
     * 
     * @param pageNum 页码（从1开始）
     * @param pageSize 每页大小
     * @return 初始化结果
     */
    public LogininforSignInitResult initLogininforSignaturesByPage(int pageNum, int pageSize) {
        log.info("开始分页初始化登录日志表签名，页码：{}，每页：{}", pageNum, pageSize);
        
        LogininforSignInitResult result = new LogininforSignInitResult();
        
        try {
            // 查询总数
            SysLogininfor queryParam = new SysLogininfor();
            List<SysLogininfor> allList = sysLogininforMapper.selectLogininforList(queryParam);
            result.totalCount = allList.size();
            
            // 计算分页
            int startIndex = (pageNum - 1) * pageSize;
            int endIndex = Math.min(startIndex + pageSize, result.totalCount);
            
            if (startIndex >= result.totalCount) {
                log.warn("页码超出范围，总记录数：{}，请求页码：{}", result.totalCount, pageNum);
                return result;
            }
            
            List<SysLogininfor> pageList = allList.subList(startIndex, endIndex);
            result.processCount = pageList.size();
            
            log.info("分页处理：总数 {}，当前页 {}/{}，处理 {} 条", 
                result.totalCount, pageNum, (result.totalCount + pageSize - 1) / pageSize, result.processCount);
            
            // 处理当前页的数据
            for (SysLogininfor logininfor : pageList) {
                try {
                    sysLogininforSignService.signLogininfor(logininfor);
                    result.successCount++;
                } catch (Exception e) {
                    result.failCount++;
                    log.error("登录日志签名失败，日志ID：{}，用户名：{}，错误：{}", 
                        logininfor.getInfoId(), logininfor.getUserName(), e.getMessage());
                }
            }
            
            log.info("分页签名初始化完成 - 页码: {}, 处理: {}, 成功: {}, 失败: {}", 
                pageNum, result.processCount, result.successCount, result.failCount);
            
        } catch (Exception e) {
            log.error("分页登录日志签名初始化异常：{}", e.getMessage(), e);
            result.errorMessage = e.getMessage();
        }
        
        return result;
    }
    
    /**
     * 获取登录日志统计信息
     * 
     * @return 统计信息
     */
    public LogininforStatistics getLogininforStatistics() {
        LogininforStatistics statistics = new LogininforStatistics();
        
        try {
            // 查询总记录数
            SysLogininfor queryParam = new SysLogininfor();
            List<SysLogininfor> allList = sysLogininforMapper.selectLogininforList(queryParam);
            statistics.totalCount = allList.size();
            
            // 这里可以添加更多统计信息，比如按状态分组等
            
        } catch (Exception e) {
            log.error("获取登录日志统计信息异常：{}", e.getMessage(), e);
        }
        
        return statistics;
    }
    
    /**
     * 登录日志签名初始化结果
     */
    public static class LogininforSignInitResult {
        public int totalCount = 0;      // 总记录数
        public int processCount = 0;    // 处理记录数
        public int successCount = 0;    // 成功数量
        public int failCount = 0;       // 失败数量
        public String errorMessage;     // 错误信息
        
        @Override
        public String toString() {
            return String.format("登录日志签名初始化结果 - 总数: %d, 处理: %d, 成功: %d, 失败: %d", 
                totalCount, processCount, successCount, failCount);
        }
    }
    
    /**
     * 登录日志统计信息
     */
    public static class LogininforStatistics {
        public int totalCount = 0;      // 总记录数
        
        @Override
        public String toString() {
            return String.format("登录日志统计 - 总记录数: %d", totalCount);
        }
    }
}
