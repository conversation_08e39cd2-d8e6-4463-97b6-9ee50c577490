package com.ruoyi.system.mapper;

import com.ruoyi.system.domain.SacSignatures;
import java.util.List;

public interface SacSignaturesMapper {
    int insert(SacSignatures record);

    int updateByPrimaryKey(SacSignatures record);

    /**
     * 根据表名和记录ID查询签名记录
     *
     * @param record 查询条件
     * @return 签名记录
     */
    SacSignatures selectByTableAndRecord(SacSignatures record);

    /**
     * 根据条件查询签名记录列表
     *
     * @param record 查询条件
     * @return 签名记录列表
     */
    List<SacSignatures> selectSacSignaturesList(SacSignatures record);

    /**
     * 根据条件批量删除签名记录
     *
     * @param record 删除条件
     * @return 删除的记录数
     */
    int deleteSacSignaturesByCondition(SacSignatures record);
}