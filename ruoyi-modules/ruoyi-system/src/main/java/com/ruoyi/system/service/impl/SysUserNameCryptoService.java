package com.ruoyi.system.service.impl;


import com.ruoyi.sac.domain.api.DecryptRequestForApi;
import com.ruoyi.sac.domain.api.EncryptRequestForApi;
import com.ruoyi.sac.domain.response.DecryptResponse;
import com.ruoyi.sac.domain.response.EncryptResponse;
import com.ruoyi.sac.service.CryptoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * 用户名加密服务
 * 提供用户名的商用密码加密和解密功能
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class SysUserNameCryptoService {

    @Autowired
    private CryptoService cryptoService;

    /**
     * 对用户名进行SAC商用密码加密
     *
     * @param userName 原始用户名
     * @return 加密后的用户名，如果加密失败则返回原用户名
     */
    public String encryptUserName(String userName) {
        if (!StringUtils.hasText(userName)) {
            return userName;
        }

        try {
            log.info("开始对用户名进行SAC加密，原用户名: {}", userName);

            // 将用户名转换为HEX编码
            String hexUserName = stringToHex(userName);
            log.debug("用户名HEX编码后长度: {}", hexUserName.length());

            EncryptRequestForApi request = new EncryptRequestForApi();
            request.setData(hexUserName);

            EncryptResponse response = cryptoService.encryptForApi(request);

            if (response != null && response.getCode() != null && response.getCode() == 200
                && response.getData() != null && StringUtils.hasText(response.getData().getEncData())) {

                String encryptedUserName = response.getData().getEncData();
                log.info("用户名SAC加密成功，加密后长度: {}", encryptedUserName.length());
                return encryptedUserName;
            } else {
                log.error("用户名SAC加密失败，响应码: {}, 响应消息: {}", 
                    response != null ? response.getCode() : "null",
                    response != null ? response.getMsg() : "null");
                return userName; // 加密失败时返回原用户名
            }
        } catch (Exception e) {
            log.error("用户名SAC加密异常: {}", e.getMessage(), e);
            return userName; // 加密异常时返回原用户名
        }
    }

    /**
     * 对用户名进行SAC商用密码解密
     *
     * @param encryptedUserName 加密后的用户名
     * @return 解密后的用户名，如果解密失败则返回原用户名
     */
    public String decryptUserName(String encryptedUserName) {
        if (!StringUtils.hasText(encryptedUserName)) {
            return encryptedUserName;
        }

        try {
            log.info("开始对用户名进行SAC解密，密文长度: {}", encryptedUserName.length());

            DecryptRequestForApi request = new DecryptRequestForApi();
            request.setData(encryptedUserName);

            DecryptResponse response = cryptoService.decryptForApi(request);

            if (response != null && response.getCode() != null && response.getCode() == 200
                && response.getData() != null && StringUtils.hasText(response.getData().getDecData())) {

                String hexUserName = response.getData().getDecData();
                log.debug("用户名解密后HEX长度: {}", hexUserName.length());

                // 将HEX编码转换回字符串
                String decryptedUserName = hexToString(hexUserName);
                log.info("用户名SAC解密成功，解密后用户名: {}", decryptedUserName);
                return decryptedUserName;
            } else {
                log.error("用户名SAC解密失败，响应码: {}, 响应消息: {}", 
                    response != null ? response.getCode() : "null",
                    response != null ? response.getMsg() : "null");
                return encryptedUserName; // 解密失败时返回原用户名
            }
        } catch (Exception e) {
            log.error("用户名SAC解密异常: {}", e.getMessage(), e);
            return encryptedUserName; // 解密异常时返回原用户名
        }
    }

    /**
     * 检查用户名是否已经加密
     * 简单判断：如果用户名长度超过64且包含特定字符，认为已加密
     *
     * @param userName 用户名
     * @return true表示已加密，false表示未加密
     */
    public boolean isEncryptedUserName(String userName) {
        if (!StringUtils.hasText(userName)) {
            return false;
        }
        
        // 加密后的用户名通常是HEX格式，长度较长且只包含0-9A-F字符
        return userName.length() > 64 && userName.matches("^[0-9A-Fa-f]+$");
    }

    /**
     * 将字符串转换为HEX编码
     *
     * @param str 原始字符串
     * @return HEX编码字符串
     */
    private String stringToHex(String str) {
        if (str == null) {
            return "";
        }
        
        StringBuilder hex = new StringBuilder();
        for (char c : str.toCharArray()) {
            hex.append(String.format("%02X", (int) c));
        }
        return hex.toString();
    }

    /**
     * 将HEX编码转换为字符串
     *
     * @param hex HEX编码字符串
     * @return 原始字符串
     */
    private String hexToString(String hex) {
        if (hex == null || hex.length() % 2 != 0) {
            return hex;
        }
        
        StringBuilder str = new StringBuilder();
        for (int i = 0; i < hex.length(); i += 2) {
            String hexPair = hex.substring(i, i + 2);
            int charCode = Integer.parseInt(hexPair, 16);
            str.append((char) charCode);
        }
        return str.toString();
    }

    /**
     * 对用户名进行脱敏显示
     *
     * @param userName 用户名
     * @return 脱敏后的用户名
     */
    private String maskUserName(String userName) {
        if (!StringUtils.hasText(userName)) {
            return userName;
        }
        
        if (userName.length() <= 3) {
            return userName.charAt(0) + "***";
        } else {
            return userName.substring(0, 2) + "***" + userName.substring(userName.length() - 1);
        }
    }
}
