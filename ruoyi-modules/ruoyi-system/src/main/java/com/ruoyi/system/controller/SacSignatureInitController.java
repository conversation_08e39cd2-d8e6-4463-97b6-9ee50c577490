package com.ruoyi.system.controller;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.service.impl.SacSignatureInitServiceImpl;
import com.ruoyi.system.service.impl.SysLogininforSignInitServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 签名初始化控制器
 */
@RestController
@RequestMapping("/sac/signature/init")
public class SacSignatureInitController extends BaseController {

    @Autowired
    private SacSignatureInitServiceImpl sacSignatureInitService;

    @Autowired
    private SysLogininforSignInitServiceImpl sysLogininforSignInitService;

    /**
     * 初始化所有表的签名
     */
    @Log(title = "签名初始化", businessType = BusinessType.OTHER)
    //@RequiresPermissions("system:signature:init")
    @PostMapping("/all")
    public AjaxResult initAllSignatures() {
        try {
            SacSignatureInitServiceImpl.SignatureInitResult result = sacSignatureInitService.initAllSignatures();
            return AjaxResult.success("签名初始化完成", result);
        } catch (Exception e) {
            return AjaxResult.error("签名初始化失败: " + e.getMessage());
        }
    }

    /**
     * 初始化登录日志签名
     */
    @Log(title = "登录日志签名初始化", businessType = BusinessType.OTHER)
    //@RequiresPermissions("system:signature:init")
    @PostMapping("/logininfor")
    public AjaxResult initLogininforSignatures(@RequestParam(defaultValue = "1000") int limit) {
        try {
            SysLogininforSignInitServiceImpl.LogininforSignInitResult result =
                sysLogininforSignInitService.initLogininforSignatures(limit);

            if (result.errorMessage != null) {
                return AjaxResult.error("登录日志签名初始化失败: " + result.errorMessage);
            }

            return AjaxResult.success("登录日志签名初始化完成", result);
        } catch (Exception e) {
            return AjaxResult.error("登录日志签名初始化失败: " + e.getMessage());
        }
    }

    /**
     * 清空指定条件的签名数据
     *
     * @param tableName 表名（必填）
     * @param tableField 字段（可选）
     * @param recordId 记录ID（可选）
     */
    @Log(title = "清空签名数据", businessType = BusinessType.DELETE)
    @DeleteMapping("/clear")
    public AjaxResult clearSignatures(
            @RequestParam String tableName,
            @RequestParam(required = false) String tableField,
            @RequestParam(required = false) String recordId) {
        try {
            // 参数验证
            if (tableName == null || tableName.trim().isEmpty()) {
                return AjaxResult.error("表名不能为空");
            }

            // 调用清空服务
            int deletedCount = sacSignatureInitService.clearSignatures(tableName.trim(), tableField, recordId);

            String message = String.format("成功清空签名数据，删除记录数: %d", deletedCount);
            return AjaxResult.success(message, deletedCount);

        } catch (Exception e) {
            return AjaxResult.error("清空签名数据失败: " + e.getMessage());
        }
    }
}
