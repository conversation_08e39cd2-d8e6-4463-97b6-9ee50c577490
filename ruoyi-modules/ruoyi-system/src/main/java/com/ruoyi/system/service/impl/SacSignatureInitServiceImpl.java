package com.ruoyi.system.service.impl;

import com.ruoyi.system.api.domain.SysUser;
import com.ruoyi.system.domain.SysRoleMenu;
import com.ruoyi.system.domain.SysUserRole;
import com.ruoyi.system.mapper.SysRoleMenuMapper;
import com.ruoyi.system.mapper.SysUserMapper;
import com.ruoyi.system.mapper.SysUserRoleMapper;
import com.ruoyi.system.mapper.SacSignaturesMapper;
import com.ruoyi.system.domain.SacSignatures;
import com.ruoyi.common.security.context.PreTenantContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 签名初始化服务实现类
 * 用于对现有数据进行批量签名初始化
 */
@Slf4j
@Service
public class SacSignatureInitServiceImpl {
    
    @Autowired
    private SysUserMapper sysUserMapper;
    
    @Autowired
    private SysUserRoleMapper sysUserRoleMapper;
    
    @Autowired
    private SysRoleMenuMapper sysRoleMenuMapper;
    
    @Autowired
    private SysUserSignServiceImpl sysUserSignService;
    
    @Autowired
    private SysPermissionSignServiceImpl sysPermissionSignService;

    @Autowired
    private PreTenantContext preTenantContext;

    @Autowired
    private SacSignaturesMapper sacSignaturesMapper;
    
    /**
     * 初始化所有表的签名
     *
     * @return 初始化结果统计
     */
    public SignatureInitResult initAllSignatures() {
        log.info("开始初始化所有表的签名...");

        // 设置app_id上下文，解决PreAppHandler多租户拦截问题
        preTenantContext.setCurrentAppId("2");

        SignatureInitResult result = new SignatureInitResult();
        
        // 初始化用户表签名
        result.userCount = initUserSignatures();
        
        // 初始化用户角色关联表签名
        result.userRoleCount = initUserRoleSignatures();
        
        // 初始化角色菜单关联表签名
        result.roleMenuCount = initRoleMenuSignatures();
        
        log.info("签名初始化完成 - 用户表: {}, 用户角色关联: {}, 角色菜单关联: {}", 
            result.userCount, result.userRoleCount, result.roleMenuCount);
        
        return result;
    }
    
    /**
     * 初始化用户表签名
     * 
     * @return 成功签名的用户数量
     */
    public int initUserSignatures() {
        log.info("开始初始化用户表签名...");
        
        int successCount = 0;
        int failCount = 0;
        
        try {
            // 查询所有有效用户
            SysUser queryUser = new SysUser();
            queryUser.setDelFlag("0"); // 未删除的用户
            List<SysUser> users = sysUserMapper.selectUserSacInit(queryUser);

            log.info("找到 {} 个用户需要初始化签名", users.size());
            
            for (SysUser user : users) {
                try {
                    // 对用户关键字段进行签名
                    sysUserSignService.signUserData(
                        user.getUserId(), 
                        user.getUserName(), 
                        user.getPassword(), 
                        user.getPhonenumber()
                    );
                    successCount++;
                } catch (Exception e) {
                    failCount++;
                    log.error("用户签名失败，用户ID：{}，用户名：{}，错误：{}", 
                        user.getUserId(), user.getUserName(), e.getMessage());
                }
            }
            
            log.info("用户表签名初始化完成 - 成功: {}, 失败: {}", successCount, failCount);
            
        } catch (Exception e) {
            log.error("用户表签名初始化异常：{}", e.getMessage(), e);
        }
        
        return successCount;
    }
    
    /**
     * 初始化用户角色关联表签名
     *
     * @return 成功签名的用户数量
     */
    public int initUserRoleSignatures() {
        log.info("开始初始化用户角色关联表签名...");

        // 设置app_id上下文
        preTenantContext.setCurrentAppId("2");

        int successCount = 0;
        int failCount = 0;

        try {
            // 查询所有用户角色关联（app_id=0和app_id=2），按用户分组
            List<SysUserRole> userRoles = sysUserRoleMapper.selectAllUserRoles();

            log.info("查询到 {} 条用户角色关联记录（app_id=0和app_id=2）", userRoles.size());

            if (userRoles.isEmpty()) {
                log.warn("未找到app_id=0或app_id=2的用户角色关联数据，请检查数据库中是否存在相关数据");
                return 0;
            }

            // 按用户ID和app_id分组，确保不同app_id的角色分别处理
            Map<String, List<Long>> userAppRoleMap = userRoles.stream()
                .collect(Collectors.groupingBy(
                    userRole -> userRole.getUserId() + "_" + userRole.getAppId(),
                    Collectors.mapping(SysUserRole::getRoleId, Collectors.toList())
                ));

            log.info("找到 {} 个用户-应用组合需要初始化角色关联签名", userAppRoleMap.size());

            for (Map.Entry<String, List<Long>> entry : userAppRoleMap.entrySet()) {
                try {
                    String userAppKey = entry.getKey();
                    String[] parts = userAppKey.split("_");
                    Long userId = Long.valueOf(parts[0]);
                    Integer appId = Integer.valueOf(parts[1]);
                    List<Long> roleIds = entry.getValue();

                    log.info("处理用户角色关联签名 - 用户ID: {}, app_id: {}, 角色数量: {}", userId, appId, roleIds.size());

                    // 对用户的所有角色关联进行整体签名
                    sysPermissionSignService.signUserRoles(userId, roleIds);
                    successCount++;
                } catch (Exception e) {
                    failCount++;
                    log.error("用户角色关联签名失败，用户-应用组合：{}，错误：{}",
                        entry.getKey(), e.getMessage());
                }
            }

            log.info("用户角色关联表签名初始化完成 - 成功: {}, 失败: {}", successCount, failCount);

        } catch (Exception e) {
            log.error("用户角色关联表签名初始化异常：{}", e.getMessage(), e);
        }

        return successCount;
    }
    
    /**
     * 初始化角色菜单关联表签名
     *
     * @return 成功签名的角色数量
     */
    public int initRoleMenuSignatures() {
        log.info("开始初始化角色菜单关联表签名...");

        int successCount = 0;
        int failCount = 0;

        try {
            // 查询所有角色菜单关联，按角色分组
            List<SysRoleMenu> roleMenus = sysRoleMenuMapper.selectAllRoleMenus();

            // 按角色ID分组
            Map<Long, List<Long>> roleMenuMap = roleMenus.stream()
                .collect(Collectors.groupingBy(
                    SysRoleMenu::getRoleId,
                    Collectors.mapping(SysRoleMenu::getMenuId, Collectors.toList())
                ));

            log.info("找到 {} 个角色需要初始化菜单关联签名", roleMenuMap.size());

            for (Map.Entry<Long, List<Long>> entry : roleMenuMap.entrySet()) {
                try {
                    Long roleId = entry.getKey();
                    List<Long> menuIds = entry.getValue();

                    // 对角色的所有菜单关联进行整体签名
                    sysPermissionSignService.signRoleMenus(roleId, menuIds);
                    successCount++;
                } catch (Exception e) {
                    failCount++;
                    log.error("角色菜单关联签名失败，角色ID：{}，错误：{}",
                        entry.getKey(), e.getMessage());
                }
            }

            log.info("角色菜单关联表签名初始化完成 - 成功: {}, 失败: {}", successCount, failCount);

        } catch (Exception e) {
            log.error("角色菜单关联表签名初始化异常：{}", e.getMessage(), e);
        }

        return successCount;
    }

    /**
     * 清空指定条件的签名数据
     *
     * @param tableName 表名（必填）
     * @param tableField 字段（可选）
     * @param recordId 记录ID（可选）
     * @return 删除的记录数
     */
    public int clearSignatures(String tableName, String tableField, String recordId) {
        log.info("开始清空签名数据 - 表名: {}, 字段: {}, 记录ID: {}", tableName, tableField, recordId);

        try {
            // 构建删除条件
            SacSignatures deleteCondition = new SacSignatures();
            deleteCondition.setTableName(tableName);

            if (tableField != null && !tableField.trim().isEmpty()) {
                deleteCondition.setTableField(tableField.trim());
            }

            if (recordId != null && !recordId.trim().isEmpty()) {
                deleteCondition.setRecordId(recordId.trim());
            }

            // 先查询符合条件的记录数（用于日志记录）
            List<SacSignatures> signaturesToDelete = sacSignaturesMapper.selectSacSignaturesList(deleteCondition);
            int totalCount = signaturesToDelete.size();

            log.info("查询到符合条件的签名记录数: {} - 表名: {}, 字段: {}, 记录ID: {}",
                totalCount, tableName, tableField, recordId);

            if (totalCount == 0) {
                log.info("未找到符合条件的签名记录，无需删除 - 表名: {}, 字段: {}, 记录ID: {}",
                    tableName, tableField, recordId);
                return 0;
            }

            // 执行批量删除操作
            int deletedCount = sacSignaturesMapper.deleteSacSignaturesByCondition(deleteCondition);

            log.info("签名数据清空完成 - 表名: {}, 字段: {}, 记录ID: {}, 预期删除: {}, 实际删除: {}",
                tableName, tableField, recordId, totalCount, deletedCount);

            return deletedCount;

        } catch (Exception e) {
            log.error("清空签名数据异常 - 表名: {}, 字段: {}, 记录ID: {}, 错误: {}",
                tableName, tableField, recordId, e.getMessage(), e);
            throw new RuntimeException("清空签名数据失败: " + e.getMessage(), e);
        }
    }

    /**
     * 签名初始化结果统计
     */
    public static class SignatureInitResult {
        public int userCount = 0;           // 用户表签名数量
        public int userRoleCount = 0;       // 用户角色关联签名数量
        public int roleMenuCount = 0;       // 角色菜单关联签名数量
        
        public int getTotalCount() {
            return userCount + userRoleCount + roleMenuCount;
        }
        
        @Override
        public String toString() {
            return String.format("签名初始化结果 - 用户表: %d, 用户角色关联: %d, 角色菜单关联: %d, 总计: %d", 
                userCount, userRoleCount, roleMenuCount, getTotalCount());
        }
    }
}
