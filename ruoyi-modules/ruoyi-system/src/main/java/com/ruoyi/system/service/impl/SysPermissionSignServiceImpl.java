package com.ruoyi.system.service.impl;

import com.ruoyi.system.domain.dto.SacSignaturesReqDto;
import com.ruoyi.system.domain.dto.VerifySignatureReqDto;
import com.ruoyi.system.service.ISacSignaturesService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 权限相关表签名服务实现类
 * 处理用户角色关联、角色菜单关联等权限数据的签名
 */
@Slf4j
@Service
public class SysPermissionSignServiceImpl {
    
    @Autowired
    private ISacSignaturesService sacSignaturesService;
    
    // 用户角色关联表常量
    private static final String USER_ROLE_TABLE_NAME = "sys_user_role";
    private static final String USER_ROLE_TABLE_FIELD = "user_id,role_ids,app_id";

    // 营销后台应用ID
    private static final Integer MARKETING_APP_ID = 2;

    // 角色菜单关联表常量
    private static final String ROLE_MENU_TABLE_NAME = "sys_role_menu";
    private static final String ROLE_MENU_TABLE_FIELD = "role_id,menu_ids";
    
    /**
     * 为用户的所有角色关联创建或更新签名
     *
     * @param userId 用户ID
     * @param roleIds 角色ID列表
     * @return 签名操作结果
     */
    public int signUserRoles(Long userId, List<Long> roleIds) {
        try {
            // 确定app_id：超级管理员(user_id=1)使用app_id=0，其他用户使用app_id=2
            Integer appId = (userId != null && userId == 1L) ? 0 : MARKETING_APP_ID;

            log.info("开始用户角色关联签名 - 用户ID: {}, 角色数量: {}, app_id: {}, 角色ID: {}", userId, roleIds.size(), appId, roleIds);

            // 构建待签名数据（用户ID + 所有角色ID + app_id）
            String dataToSign = buildUserRolesSignData(userId, roleIds);

            // 记录签名原文详细信息
            log.info("用户角色关联签名原文 - 用户ID: {}, 角色数量: {}, app_id: {}, 数据长度: {}, 原文内容: [{}]",
                userId, roleIds.size(), appId,
                dataToSign != null ? dataToSign.length() : 0,
                dataToSign != null ? dataToSign : "null");

            // 构建签名请求
            SacSignaturesReqDto reqDto = new SacSignaturesReqDto();
            reqDto.setTableName(USER_ROLE_TABLE_NAME);
            reqDto.setTableField(USER_ROLE_TABLE_FIELD);
            reqDto.setRecordId(String.valueOf(userId)); // 以用户ID作为记录ID
            reqDto.setDataToSign(dataToSign);

            log.info("用户角色关联签名请求 - 表名: {}, 字段: {}, 记录ID: {}",
                USER_ROLE_TABLE_NAME, USER_ROLE_TABLE_FIELD, userId);

            // 执行签名
            int result = sacSignaturesService.updateSignatures(reqDto);

            if (result > 0) {
                log.info("用户角色关联签名成功 - 用户ID: {}, 角色数量: {}, app_id: {}, 操作结果: {}", userId, roleIds.size(), appId, result);
            } else {
                log.info("用户角色关联签名失败 - 用户ID: {}, 角色数量: {}, app_id: {}, 操作结果: {}", userId, roleIds.size(), appId, result);
            }

            return result;
        } catch (Exception e) {
            Integer appId = (userId != null && userId == 1L) ? 0 : MARKETING_APP_ID;
            log.error("用户角色关联签名异常 - 用户ID: {}, 角色数量: {}, app_id: {}, 错误: {}", userId, roleIds.size(), appId, e.getMessage(), e);
            throw new RuntimeException("用户角色关联签名失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 验证用户的所有角色关联签名
     *
     * @param userId 用户ID
     * @param roleIds 角色ID列表
     * @return 验证结果
     */
    public boolean verifyUserRoles(Long userId, List<Long> roleIds) {
        try {
            // 构建待验证数据
            String dataToVerify = buildUserRolesSignData(userId, roleIds);

            // 构建验证请求
            VerifySignatureReqDto reqDto = new VerifySignatureReqDto();
            reqDto.setTableName(USER_ROLE_TABLE_NAME);
            reqDto.setTableField(USER_ROLE_TABLE_FIELD);
            reqDto.setRecordId(String.valueOf(userId));
            reqDto.setDataToVerify(dataToVerify);

            // 执行验证
            boolean result = sacSignaturesService.verifySignature(reqDto);

            if (result) {
                log.debug("用户角色关联签名验证通过，用户ID：{}，角色数量：{}", userId, roleIds.size());
            } else {
                log.warn("用户角色关联签名验证失败，用户ID：{}，角色数量：{}", userId, roleIds.size());
            }

            return result;
        } catch (Exception e) {
            log.error("用户角色关联签名验证异常，用户ID：{}，角色数量：{}，错误：{}", userId, roleIds.size(), e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 为角色的所有菜单关联创建或更新签名
     *
     * @param roleId 角色ID
     * @param menuIds 菜单ID列表
     * @return 签名操作结果
     */
    public int signRoleMenus(Long roleId, List<Long> menuIds) {
        try {
            log.info("开始角色菜单关联签名 - 角色ID: {}, 菜单数量: {}, 菜单ID: {}", roleId, menuIds.size(), menuIds);

            // 构建待签名数据（角色ID + 所有菜单ID）
            String dataToSign = buildRoleMenusSignData(roleId, menuIds);

            // 记录签名原文详细信息
            log.info("角色菜单关联签名原文 - 角色ID: {}, 菜单数量: {}, 数据长度: {}, 原文内容: [{}]",
                roleId, menuIds.size(),
                dataToSign != null ? dataToSign.length() : 0,
                dataToSign != null ? dataToSign : "null");

            // 构建签名请求
            SacSignaturesReqDto reqDto = new SacSignaturesReqDto();
            reqDto.setTableName(ROLE_MENU_TABLE_NAME);
            reqDto.setTableField(ROLE_MENU_TABLE_FIELD);
            reqDto.setRecordId(String.valueOf(roleId)); // 以角色ID作为记录ID
            reqDto.setDataToSign(dataToSign);

            log.info("角色菜单关联签名请求 - 表名: {}, 字段: {}, 记录ID: {}",
                ROLE_MENU_TABLE_NAME, ROLE_MENU_TABLE_FIELD, roleId);

            // 执行签名
            int result = sacSignaturesService.updateSignatures(reqDto);

            if (result > 0) {
                log.info("角色菜单关联签名成功 - 角色ID: {}, 菜单数量: {}, 操作结果: {}", roleId, menuIds.size(), result);
            } else {
                log.warn("角色菜单关联签名失败 - 角色ID: {}, 菜单数量: {}, 操作结果: {}", roleId, menuIds.size(), result);
            }

            return result;
        } catch (Exception e) {
            log.error("角色菜单关联签名异常 - 角色ID: {}, 菜单数量: {}, 错误: {}", roleId, menuIds.size(), e.getMessage(), e);
            throw new RuntimeException("角色菜单关联签名失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 验证角色的所有菜单关联签名
     *
     * @param roleId 角色ID
     * @param menuIds 菜单ID列表
     * @return 验证结果
     */
    public boolean verifyRoleMenus(Long roleId, List<Long> menuIds) {
        try {
            // 构建待验证数据
            String dataToVerify = buildRoleMenusSignData(roleId, menuIds);

            // 构建验证请求
            VerifySignatureReqDto reqDto = new VerifySignatureReqDto();
            reqDto.setTableName(ROLE_MENU_TABLE_NAME);
            reqDto.setTableField(ROLE_MENU_TABLE_FIELD);
            reqDto.setRecordId(String.valueOf(roleId));
            reqDto.setDataToVerify(dataToVerify);

            // 执行验证
            boolean result = sacSignaturesService.verifySignature(reqDto);

            if (result) {
                log.debug("角色菜单关联签名验证通过，角色ID：{}，菜单数量：{}", roleId, menuIds.size());
            } else {
                log.warn("角色菜单关联签名验证失败，角色ID：{}，菜单数量：{}", roleId, menuIds.size());
            }

            return result;
        } catch (Exception e) {
            log.error("角色菜单关联签名验证异常，角色ID：{}，菜单数量：{}，错误：{}", roleId, menuIds.size(), e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 构建用户角色关联的签名数据
     * 拼接user_id、所有role_id（按ID排序）和app_id
     */
    private String buildUserRolesSignData(Long userId, List<Long> roleIds) {
        StringBuilder sb = new StringBuilder();
        sb.append(userId != null ? userId.toString() : "");

        if (roleIds != null && !roleIds.isEmpty()) {
            // 对角色ID进行排序，确保签名的一致性
            roleIds.stream()
                .sorted()
                .forEach(roleId -> sb.append(roleId.toString()));
        }

        // 确定app_id：超级管理员(user_id=1)使用app_id=0，其他用户使用app_id=2
        Integer appId = (userId != null && userId == 1L) ? 0 : MARKETING_APP_ID;
        sb.append(appId.toString());

        return sb.toString();
    }

    /**
     * 构建角色菜单关联的签名数据
     * 拼接role_id和所有menu_id（按ID排序）
     */
    private String buildRoleMenusSignData(Long roleId, List<Long> menuIds) {
        StringBuilder sb = new StringBuilder();
        sb.append(roleId != null ? roleId.toString() : "");

        if (menuIds != null && !menuIds.isEmpty()) {
            // 对菜单ID进行排序，确保签名的一致性
            menuIds.stream()
                .sorted()
                .forEach(menuId -> sb.append(menuId.toString()));
        }

        return sb.toString();
    }
    
    /**
     * 签名用户角色关联（兼容数组参数）
     *
     * @param userId 用户ID
     * @param roleIds 角色ID数组
     */
    public int signUserRoles(Long userId, Long[] roleIds) {
        if (roleIds == null || roleIds.length == 0) {
            return 0;
        }

        List<Long> roleIdList = java.util.Arrays.asList(roleIds);
        return signUserRoles(userId, roleIdList);
    }

    /**
     * 签名角色菜单关联（兼容数组参数）
     *
     * @param roleId 角色ID
     * @param menuIds 菜单ID数组
     */
    public int signRoleMenus(Long roleId, Long[] menuIds) {
        if (menuIds == null || menuIds.length == 0) {
            return 0;
        }

        List<Long> menuIdList = java.util.Arrays.asList(menuIds);
        return signRoleMenus(roleId, menuIdList);
    }
}
