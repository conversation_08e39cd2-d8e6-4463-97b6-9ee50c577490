package com.ruoyi.system.service;

import com.ruoyi.system.domain.SacSignatures;
import com.ruoyi.system.domain.dto.SacSignaturesReqDto;
import com.ruoyi.system.domain.dto.VerifySignatureReqDto;

/**
 * 密评签名服务接口
 */
public interface ISacSignaturesService {

    
    /**
     * 更新签名记录
     * 
     * @param signatures 签名信息
     * @return 结果
     */
    public int updateSignatures(SacSignaturesReqDto signatures);
    
    /**
     * 根据表名、字段和记录ID查询签名记录
     * 
     * @param tableName 表名
     * @param tableField 表字段
     * @param recordId 记录ID
     * @return 签名记录
     */
    public SacSignatures selectSignaturesByTableAndRecord(String tableName,String tableField,String recordId);
    
    /**
     * 验证签名
     * @return 验证结果
     */
    public boolean verifySignature(VerifySignatureReqDto reqDto);
} 