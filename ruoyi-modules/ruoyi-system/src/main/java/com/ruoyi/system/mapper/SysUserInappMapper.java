package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.SysUserInapp;

/**
 * 用户应用租户Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-02-01
 */
public interface SysUserInappMapper 
{
    /**
     * 查询用户应用租户
     * 
     * @param userInappId 用户应用租户主键
     * @return 用户应用租户
     */
    public SysUserInapp selectSysUserInappByUserInappId(Long userInappId);

    /**
     * 查询用户应用租户列表
     * 
     * @param sysUserInapp 用户应用租户
     * @return 用户应用租户集合
     */
    public List<SysUserInapp> selectSysUserInappList(SysUserInapp sysUserInapp);

    /**
     * 新增用户应用租户
     * 
     * @param sysUserInapp 用户应用租户
     * @return 结果
     */
    public int insertSysUserInapp(SysUserInapp sysUserInapp);

    /**
     * 修改用户应用租户
     * 
     * @param sysUserInapp 用户应用租户
     * @return 结果
     */
    public int updateSysUserInapp(SysUserInapp sysUserInapp);

    /**
     * 删除用户应用租户
     * 
     * @param userInappId 用户应用租户主键
     * @return 结果
     */
    public int deleteSysUserInappByUserInappId(Long userInappId);

    /**
     * 批量删除用户应用租户
     * 
     * @param userInappIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSysUserInappByUserInappIds(Long[] userInappIds);
}
