import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import javax.sql.DataSource;
import java.util.ArrayList;
import java.util.List;

public class ListTest {



    @Test
    public void test1() {
//        System.out.println(dataSource);
        List<String> list = new ArrayList<>();
        list.add("小伙子01");
        list.add("小伙子02");
        list.add("小伙子03");
        list.add("小伙子04");
        list.forEach(r -> r.replace("小伙子", "老头"));

        System.out.println(list.toString());
    }
}
