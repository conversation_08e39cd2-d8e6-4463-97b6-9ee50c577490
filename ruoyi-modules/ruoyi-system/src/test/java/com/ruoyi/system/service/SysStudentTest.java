package com.ruoyi.system.service;


import com.ruoyi.system.RuoYiSystemApplication;
import com.ruoyi.system.domain.SysStudent;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = RuoYiSystemApplication.class)
public class SysStudentTest {

    @Autowired
    ISysStudentService service;

    @Test
    public void testQueryList() {
        SysStudent st = new SysStudent();
        List<SysStudent> list = service.queryList(st);

        System.out.println(list.size());
    }

}
