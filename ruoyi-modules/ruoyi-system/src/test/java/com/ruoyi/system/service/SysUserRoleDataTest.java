package com.ruoyi.system.service;

import com.ruoyi.system.domain.SysUserRole;
import com.ruoyi.system.mapper.SysUserRoleMapper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

/**
 * 用户角色数据测试类
 * 用于验证app_id=0和app_id=2的数据查询
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class SysUserRoleDataTest {
    
    @Autowired
    private SysUserRoleMapper sysUserRoleMapper;
    
    /**
     * 测试查询app_id=0和app_id=2的用户角色关联数据
     */
    @Test
    public void testSelectAllUserRoles() {
        List<SysUserRole> userRoles = sysUserRoleMapper.selectAllUserRoles();

        System.out.println("查询到的用户角色关联数据数量: " + userRoles.size());

        for (SysUserRole userRole : userRoles) {
            System.out.println(String.format("用户ID: %d, 角色ID: %d, 应用ID: %d",
                userRole.getUserId(), userRole.getRoleId(), userRole.getAppId()));
        }

        if (userRoles.isEmpty()) {
            System.out.println("警告：未找到app_id=0或app_id=2的用户角色关联数据");
            System.out.println("请检查数据库中sys_user_role表是否存在app_id=0或app_id=2的记录");
        }
    }
    
    /**
     * 测试查询特定用户的角色列表
     */
    @Test
    public void testSelectRoleListByUserId() {
        // 这里需要替换为实际存在的用户ID
        Long testUserId = 1L;
        
        List<Long> roleIds = sysUserRoleMapper.selectRoleListBySac(testUserId, 0L);
        
        System.out.println(String.format("用户ID %d 在app_id=2下的角色数量: %d", testUserId, roleIds.size()));
        
        for (Long roleId : roleIds) {
            System.out.println("角色ID: " + roleId);
        }
        
        if (roleIds.isEmpty()) {
            System.out.println(String.format("警告：用户ID %d 在app_id=2下没有角色关联", testUserId));
        }
    }
}
