package com.ruoyi.system.service;

import com.ruoyi.system.domain.dto.SacSignaturesReqDto;
import com.ruoyi.system.domain.dto.VerifySignatureReqDto;
import com.ruoyi.system.service.impl.SysPermissionSignServiceImpl;
import com.ruoyi.system.service.impl.SysUserSignServiceImpl;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

import static org.junit.Assert.*;

/**
 * 签名服务测试类
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class SacSignaturesServiceTest {
    
    @Autowired
    private ISacSignaturesService sacSignaturesService;
    
    @Autowired
    private SysUserSignServiceImpl sysUserSignService;

    @Autowired
    private SysPermissionSignServiceImpl sysPermissionSignService;
    
    /**
     * 测试基础签名功能
     */
    @Test
    public void testBasicSignature() {
        // 准备测试数据
        SacSignaturesReqDto reqDto = new SacSignaturesReqDto();
        reqDto.setTableName("test_table");
        reqDto.setTableField("test_field");
        reqDto.setRecordId("123");
        reqDto.setDataToSign("test_data");
        
        // 执行签名
        int result = sacSignaturesService.updateSignatures(reqDto);
        
        // 验证结果
        assertTrue("签名应该成功", result > 0);
        
        // 验证签名
        VerifySignatureReqDto verifyDto = new VerifySignatureReqDto();
        verifyDto.setTableName("test_table");
        verifyDto.setTableField("test_field");
        verifyDto.setRecordId("123");
        verifyDto.setDataToVerify("test_data");
        
        boolean verifyResult = sacSignaturesService.verifySignature(verifyDto);
        assertTrue("签名验证应该通过", verifyResult);
    }
    
    /**
     * 测试sys_user表签名功能
     */
    @Test
    public void testSysUserSignature() {
        // 准备测试数据
        Long userId = 1001L;
        String userName = "testuser";
        String password = "testpassword123";
        String phoneNumber = "13800138000";
        
        // 执行签名
        int signResult = sysUserSignService.signUserData(userId, userName, password, phoneNumber);
        assertTrue("用户数据签名应该成功", signResult > 0);
        
        // 验证签名
        boolean verifyResult = sysUserSignService.verifyUserData(userId, userName, password, phoneNumber);
        assertTrue("用户数据签名验证应该通过", verifyResult);
        
        // 测试数据被篡改的情况
        boolean tamperResult = sysUserSignService.verifyUserData(userId, userName, "tampered_password", phoneNumber);
        assertFalse("篡改后的数据签名验证应该失败", tamperResult);
    }
    
    /**
     * 测试签名更新功能
     */
    @Test
    public void testSignatureUpdate() {
        // 准备测试数据
        Long userId = 1002L;
        String userName = "updateuser";
        String password1 = "password1";
        String password2 = "password2";
        String phoneNumber = "13900139000";
        
        // 第一次签名
        int result1 = sysUserSignService.signUserData(userId, userName, password1, phoneNumber);
        assertTrue("第一次签名应该成功", result1 > 0);
        
        // 验证第一次签名
        boolean verify1 = sysUserSignService.verifyUserData(userId, userName, password1, phoneNumber);
        assertTrue("第一次签名验证应该通过", verify1);
        
        // 更新密码并重新签名
        int result2 = sysUserSignService.signUserData(userId, userName, password2, phoneNumber);
        assertTrue("第二次签名应该成功", result2 > 0);
        
        // 验证新签名
        boolean verify2 = sysUserSignService.verifyUserData(userId, userName, password2, phoneNumber);
        assertTrue("新签名验证应该通过", verify2);
        
        // 验证旧密码应该失败
        boolean verifyOld = sysUserSignService.verifyUserData(userId, userName, password1, phoneNumber);
        assertFalse("旧密码签名验证应该失败", verifyOld);
    }
    
    /**
     * 测试未签名数据的验证
     */
    @Test
    public void testUnsignedDataVerification() {
        // 准备测试数据（使用一个不存在的用户ID）
        Long userId = 9999L;
        String userName = "nonexistentuser";
        String password = "password";
        String phoneNumber = "13700137000";

        // 验证未签名的数据（应该返回true，表示忽略未签名数据）
        boolean verifyResult = sysUserSignService.verifyUserData(userId, userName, password, phoneNumber);
        assertTrue("未签名数据验证应该返回true（忽略）", verifyResult);
    }

    /**
     * 测试权限关联签名功能
     */
    @Test
    public void testPermissionSignature() {
        // 测试用户角色关联签名
        Long userId = 1001L;
        List<Long> roleIds = java.util.Arrays.asList(2L, 3L);

        int userRoleResult = sysPermissionSignService.signUserRoles(userId, roleIds);
        assertTrue("用户角色关联签名应该成功", userRoleResult > 0);

        boolean userRoleVerify = sysPermissionSignService.verifyUserRoles(userId, roleIds);
        assertTrue("用户角色关联签名验证应该通过", userRoleVerify);

        // 测试角色菜单关联签名
        Long roleId = 2L;
        List<Long> menuIds = java.util.Arrays.asList(1001L, 1002L);

        int roleMenuResult = sysPermissionSignService.signRoleMenus(roleId, menuIds);
        assertTrue("角色菜单关联签名应该成功", roleMenuResult > 0);

        boolean roleMenuVerify = sysPermissionSignService.verifyRoleMenus(roleId, menuIds);
        assertTrue("角色菜单关联签名验证应该通过", roleMenuVerify);
    }
}
