package com.ruoyi.system.service;

import com.ruoyi.system.api.domain.SysUser;
import com.ruoyi.system.mapper.SysUserMapper;
import com.ruoyi.system.service.impl.SysPasswordCryptoService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * 密码加密初始化服务测试类
 * 
 * <AUTHOR>
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
public class SysPasswordCryptoInitServiceTest {

    @Autowired
    private SysPasswordCryptoService sysPasswordCryptoService;

    @Autowired
    private SysUserMapper sysUserMapper;

    /**
     * 测试用户ID为1的密码加密和解密
     */
    @Test
    public void testPasswordCryptoForUser1() {
        log.info("开始测试用户ID为1的密码加密和解密...");

        try {
            // 1. 查询用户ID为1的用户信息
            SysUser user = sysUserMapper.selectUserById(1L);
            if (user == null) {
                log.error("用户ID为1的用户不存在，测试终止");
                return;
            }

            log.info("找到用户 - ID: {}, 用户名: {}, 原始密码长度: {}",
                user.getUserId(), user.getUserName(),
                user.getPassword() != null ? user.getPassword().length() : 0);

            // 记录原始密码（用于后续验证）
            String originalPassword = user.getPassword();
            log.info("原始密码: {}", originalPassword);

            // 2. 检查密码格式
            boolean isBCryptFormat = sysPasswordCryptoService.isBCryptPassword(originalPassword);
            boolean isAlreadyEncrypted = sysPasswordCryptoService.isAlreadyEncrypted(originalPassword);

            log.info("密码格式检查 - 是BCrypt格式: {}, 已加密: {}", isBCryptFormat, isAlreadyEncrypted);

            // 3. 测试单独的加密功能
            log.info("=== 测试密码加密功能 ===");
            String encryptedPassword = sysPasswordCryptoService.encryptPassword(originalPassword);
            log.info("加密后密码: {}", encryptedPassword);
            log.info("加密后密码长度: {}", encryptedPassword != null ? encryptedPassword.length() : 0);

            // 4. 测试单独的解密功能
            log.info("=== 测试密码解密功能 ===");
            String decryptedPassword = sysPasswordCryptoService.decryptPassword(encryptedPassword);
            log.info("解密后密码: {}", decryptedPassword);
            log.info("解密后密码长度: {}", decryptedPassword != null ? decryptedPassword.length() : 0);

            // 5. 验证加密解密的一致性
            boolean isConsistent = originalPassword.equals(decryptedPassword);
            log.info("加密解密一致性验证: {}", isConsistent ? "通过" : "失败");

            if (!isConsistent) {
                log.error("加密解密不一致！原始: [{}], 解密后: [{}]", originalPassword, decryptedPassword);
            }

            log.info("测试完成！");

        } catch (Exception e) {
            log.error("测试过程中发生异常: {}", e.getMessage(), e);
        }
    }



    /**
     * 测试密码格式判断功能
     */
    @Test
    public void testPasswordFormatCheck() {
        log.info("开始测试密码格式判断功能...");
        
        // 测试BCrypt格式密码
        String bcryptPassword = "$2a$10$abcdefghijklmnopqrstuvwxyz1234567890";
        log.info("BCrypt密码测试: {}", bcryptPassword);
        log.info("是否为BCrypt格式: {}", sysPasswordCryptoService.isBCryptPassword(bcryptPassword));
        log.info("是否已加密: {}", sysPasswordCryptoService.isAlreadyEncrypted(bcryptPassword));
        
        // 测试非BCrypt格式密码（模拟SAC加密后的密码）
        String encryptedPassword = "1a2b3c4d5e6f7g8h9i0j";
        log.info("加密密码测试: {}", encryptedPassword);
        log.info("是否为BCrypt格式: {}", sysPasswordCryptoService.isBCryptPassword(encryptedPassword));
        log.info("是否已加密: {}", sysPasswordCryptoService.isAlreadyEncrypted(encryptedPassword));
        
        log.info("密码格式判断测试完成！");
    }

    /**
     * 测试HEX编码转换功能
     */
    @Test
    public void testHexConversion() {
        log.info("开始测试HEX编码转换功能...");

        // 测试字符串
        String testPassword = "$2a$10$abcdefghijklmnopqrstuvwxyz1234567890";
        log.info("原始密码: {}", testPassword);

        // 手动进行HEX编码（模拟内部逻辑）
        String hexEncoded = stringToHex(testPassword);
        log.info("HEX编码后: {}", hexEncoded);
        log.info("HEX编码长度: {}", hexEncoded.length());

        // 手动进行HEX解码（模拟内部逻辑）
        String hexDecoded = hexToString(hexEncoded);
        log.info("HEX解码后: {}", hexDecoded);

        // 验证编码解码一致性
        boolean isHexConsistent = testPassword.equals(hexDecoded);
        log.info("HEX编码解码一致性: {}", isHexConsistent ? "通过" : "失败");

        if (!isHexConsistent) {
            log.error("HEX编码解码不一致！原始: [{}], 解码后: [{}]", testPassword, hexDecoded);
        }

        log.info("HEX编码转换测试完成！");
    }

    /**
     * 将字符串转换为HEX编码
     */
    private String stringToHex(String str) {
        if (str == null) {
            return null;
        }

        byte[] bytes = str.getBytes(java.nio.charset.StandardCharsets.UTF_8);
        StringBuilder hexString = new StringBuilder();

        for (byte b : bytes) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                hexString.append('0');
            }
            hexString.append(hex);
        }

        return hexString.toString().toUpperCase();
    }

    /**
     * 将HEX编码字符串转换为原始字符串
     */
    private String hexToString(String hexStr) {
        if (hexStr == null || hexStr.length() % 2 != 0) {
            return null;
        }

        byte[] bytes = new byte[hexStr.length() / 2];

        for (int i = 0; i < hexStr.length(); i += 2) {
            String hexByte = hexStr.substring(i, i + 2);
            bytes[i / 2] = (byte) Integer.parseInt(hexByte, 16);
        }

        return new String(bytes, java.nio.charset.StandardCharsets.UTF_8);
    }
}
