package com.ruoyi.system.service;


import com.ruoyi.common.security.context.PreTenantContext;
import com.ruoyi.system.RuoYiSystemApplication;
import com.ruoyi.system.domain.SysMenu;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = RuoYiSystemApplication.class)
public class SysMenuTest {

    @Autowired
    ISysMenuService service;

    @Autowired
    private PreTenantContext context;

    @Test
    public void testQueryList() {
        context.setCurrentAppId("1");

        List<SysMenu> list = service.selectMenuTreeByUserId(1L);

        System.out.println(list.size());
    }

}
