package com.ruoyi.system.service;


import com.ruoyi.common.security.context.PreTenantContext;
import com.ruoyi.system.RuoYiSystemApplication;
import com.ruoyi.system.api.domain.SysRole;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = RuoYiSystemApplication.class)
public class SysRoleTest {

    @Autowired
    ISysRoleService service;

    @Autowired
    private PreTenantContext context;

    @Test
    public void testQueryList() {
        context.setCurrentAppId("1");

        SysRole q = new SysRole();
        List<SysRole> list = service.selectRoleList(q);

        System.out.println(list.size());
    }

    @Test
    public void testQueryListWithoutAPPId() {
        SysRole q = new SysRole();
        List<SysRole> list = service.selectRoleList(q);

        System.out.println(list.size());
    }

}
