package com.ruoyi.system.service;

import com.ruoyi.system.service.impl.SacSignatureInitServiceImpl;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import static org.junit.Assert.*;

/**
 * 签名数据清空服务测试类
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class SacSignatureClearServiceTest {
    
    @Autowired
    private SacSignatureInitServiceImpl sacSignatureInitService;
    
    /**
     * 测试清空指定表的所有签名
     */
    @Test
    public void testClearSignaturesByTableName() {
        String tableName = "sys_user";
        
        int deletedCount = sacSignatureInitService.clearSignatures(tableName, null, null);
        
        assertTrue("删除数量应该大于等于0", deletedCount >= 0);
        System.out.println(String.format("清空表 %s 的签名数据，删除记录数: %d", tableName, deletedCount));
    }
    
    /**
     * 测试清空指定表和字段的签名
     */
    @Test
    public void testClearSignaturesByTableAndField() {
        String tableName = "sys_user";
        String tableField = "user_name,password,phonenumber";
        
        int deletedCount = sacSignatureInitService.clearSignatures(tableName, tableField, null);
        
        assertTrue("删除数量应该大于等于0", deletedCount >= 0);
        System.out.println(String.format("清空表 %s 字段 %s 的签名数据，删除记录数: %d", 
            tableName, tableField, deletedCount));
    }
    
    /**
     * 测试清空指定记录的签名
     */
    @Test
    public void testClearSignaturesBySpecificRecord() {
        String tableName = "sys_user";
        String tableField = "user_name,password,phonenumber";
        String recordId = "1";
        
        int deletedCount = sacSignatureInitService.clearSignatures(tableName, tableField, recordId);
        
        assertTrue("删除数量应该大于等于0", deletedCount >= 0);
        System.out.println(String.format("清空表 %s 字段 %s 记录ID %s 的签名数据，删除记录数: %d", 
            tableName, tableField, recordId, deletedCount));
    }
    
    /**
     * 测试清空不存在的表签名
     */
    @Test
    public void testClearSignaturesNonExistentTable() {
        String tableName = "non_existent_table";
        
        int deletedCount = sacSignatureInitService.clearSignatures(tableName, null, null);
        
        assertEquals("不存在的表应该删除0条记录", 0, deletedCount);
        System.out.println(String.format("清空不存在的表 %s 的签名数据，删除记录数: %d", tableName, deletedCount));
    }
    
    /**
     * 测试清空用户角色关联表签名
     */
    @Test
    public void testClearUserRoleSignatures() {
        String tableName = "sys_user_role";
        String tableField = "user_id,role_ids,app_id";
        
        int deletedCount = sacSignatureInitService.clearSignatures(tableName, tableField, null);
        
        assertTrue("删除数量应该大于等于0", deletedCount >= 0);
        System.out.println(String.format("清空用户角色关联表签名数据，删除记录数: %d", deletedCount));
    }
    
    /**
     * 测试清空角色菜单关联表签名
     */
    @Test
    public void testClearRoleMenuSignatures() {
        String tableName = "sys_role_menu";
        String tableField = "role_id,menu_ids";
        
        int deletedCount = sacSignatureInitService.clearSignatures(tableName, tableField, null);
        
        assertTrue("删除数量应该大于等于0", deletedCount >= 0);
        System.out.println(String.format("清空角色菜单关联表签名数据，删除记录数: %d", deletedCount));
    }
    
    /**
     * 测试清空登录日志表签名
     */
    @Test
    public void testClearLogininforSignatures() {
        String tableName = "sys_logininfor";
        String tableField = "user_name,status,ipaddr,msg,access_time";
        
        int deletedCount = sacSignatureInitService.clearSignatures(tableName, tableField, null);
        
        assertTrue("删除数量应该大于等于0", deletedCount >= 0);
        System.out.println(String.format("清空登录日志表签名数据，删除记录数: %d", deletedCount));
    }
}
