package com.ruoyi.system.service;

import com.ruoyi.system.service.impl.SysPermissionSignServiceImpl;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

import static org.junit.Assert.*;

/**
 * 权限签名服务测试类
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class SysPermissionSignServiceTest {
    
    @Autowired
    private SysPermissionSignServiceImpl sysPermissionSignService;
    
    /**
     * 测试用户角色关联签名功能
     */
    @Test
    public void testUserRoleSignature() {
        // 准备测试数据
        Long userId = 1001L;
        List<Long> roleIds = java.util.Arrays.asList(2L, 3L, 4L);

        // 执行签名
        int signResult = sysPermissionSignService.signUserRoles(userId, roleIds);
        assertTrue("用户角色关联签名应该成功", signResult > 0);

        // 验证签名
        boolean verifyResult = sysPermissionSignService.verifyUserRoles(userId, roleIds);
        assertTrue("用户角色关联签名验证应该通过", verifyResult);

        // 测试数据被篡改的情况
        List<Long> tamperedRoleIds = java.util.Arrays.asList(2L, 3L, 999L);
        boolean tamperResult = sysPermissionSignService.verifyUserRoles(userId, tamperedRoleIds);
        assertFalse("篡改后的数据签名验证应该失败", tamperResult);
    }
    
    /**
     * 测试角色菜单关联签名功能
     */
    @Test
    public void testRoleMenuSignature() {
        // 准备测试数据
        Long roleId = 2L;
        List<Long> menuIds = java.util.Arrays.asList(1001L, 1002L, 1003L);

        // 执行签名
        int signResult = sysPermissionSignService.signRoleMenus(roleId, menuIds);
        assertTrue("角色菜单关联签名应该成功", signResult > 0);

        // 验证签名
        boolean verifyResult = sysPermissionSignService.verifyRoleMenus(roleId, menuIds);
        assertTrue("角色菜单关联签名验证应该通过", verifyResult);

        // 测试数据被篡改的情况
        List<Long> tamperedMenuIds = java.util.Arrays.asList(1001L, 1002L, 999L);
        boolean tamperResult = sysPermissionSignService.verifyRoleMenus(roleId, tamperedMenuIds);
        assertFalse("篡改后的数据签名验证应该失败", tamperResult);
    }
    

    
    /**
     * 测试签名更新功能
     */
    @Test
    public void testSignatureUpdate() {
        // 准备测试数据
        Long userId = 1003L;
        List<Long> roleIds1 = java.util.Arrays.asList(2L, 3L);
        List<Long> roleIds2 = java.util.Arrays.asList(3L, 4L, 5L);

        // 第一次签名
        int result1 = sysPermissionSignService.signUserRoles(userId, roleIds1);
        assertTrue("第一次签名应该成功", result1 > 0);

        // 验证第一次签名
        boolean verify1 = sysPermissionSignService.verifyUserRoles(userId, roleIds1);
        assertTrue("第一次签名验证应该通过", verify1);

        // 更新为新的角色关联并重新签名
        int result2 = sysPermissionSignService.signUserRoles(userId, roleIds2);
        assertTrue("第二次签名应该成功", result2 > 0);

        // 验证新签名
        boolean verify2 = sysPermissionSignService.verifyUserRoles(userId, roleIds2);
        assertTrue("新签名验证应该通过", verify2);

        // 验证旧关联应该失败
        boolean verifyOld = sysPermissionSignService.verifyUserRoles(userId, roleIds1);
        assertFalse("旧角色关联签名验证应该失败", verifyOld);
    }

    /**
     * 测试未签名数据的验证
     */
    @Test
    public void testUnsignedDataVerification() {
        // 准备测试数据（使用不存在的ID）
        Long userId = 9999L;
        List<Long> roleIds = java.util.Arrays.asList(9999L);

        // 验证未签名的数据（应该返回true，表示忽略未签名数据）
        boolean verifyResult = sysPermissionSignService.verifyUserRoles(userId, roleIds);
        assertTrue("未签名数据验证应该返回true（忽略）", verifyResult);
    }
}
