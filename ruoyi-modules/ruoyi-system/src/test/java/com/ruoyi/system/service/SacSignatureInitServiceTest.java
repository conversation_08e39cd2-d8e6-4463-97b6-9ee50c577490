package com.ruoyi.system.service;

import com.ruoyi.system.service.impl.SacSignatureInitServiceImpl;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import static org.junit.Assert.*;

/**
 * 签名初始化服务测试类
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class SacSignatureInitServiceTest {

    @Autowired
    private SacSignatureInitServiceImpl sacSignatureInitService;

    /**
     * 测试所有表签名初始化
     */
    @Test
    public void testInitAllSignatures() {
        SacSignatureInitServiceImpl.SignatureInitResult result = sacSignatureInitService.initAllSignatures();

        assertNotNull("初始化结果不应为空", result);
        assertTrue("总处理记录数应该大于等于0", result.getTotalCount() >= 0);

        System.out.println("签名初始化结果：" + result.toString());
        System.out.println("用户表: " + result.userCount);
        System.out.println("用户角色关联: " + result.userRoleCount);
        System.out.println("角色菜单关联: " + result.roleMenuCount);
        System.out.println("总计: " + result.getTotalCount());
    }
}
