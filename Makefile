all: mvnb clean build push
build: srv
push: psrbuv

#         ---  tag 命名规范   ---
# 测试环境  默认测试版本号 alpha 分支版本测试   1.0.1-alpha
# 预发布环境 默认测试版本号 beta 分支版本测试   1.0.1-beta
# 生产环境 默认版本号 latest 分支版本   1.0.1 或 1.0.1-release

TAG=
ENV=
ifdef env
	ENV = $(env)
else
	ENV = test
endif
ifdef tag
	TAG = $(tag)
else
	TAG = alpha
endif

# so:
# 	mvn  sonar:sonar -D sonar.projectKey=ECS-DEVOPS-BFF -D sonar.host.url=http://sonarqube.juneyaoair.com:9000  -D sonar.login=****************************************
mvnb:
	mvn clean
	mvn package -Dmaven.test.skip=true
clean:
	docker image prune -f
srv:
	docker build -f ruoyi-modules/ruoyi-file/Dockerfile -t  harbor.hoair.cn/ho-ecsm/ruoyi-file:$(TAG) .
psrbuv:
	docker push harbor.hoair.cn/ho-ecsm/ruoyi-file:$(TAG)

# push前请执行
# docker login harbor.hoair.cn
# admin   Harbor12345

run:
ifdef name
	docker run --name=call-$(name) -v D:/opt/log/:/opt/log/ --restart=always -e "SPRING_PROFILES_ACTIVE=$(ENV)" --network=host -d harbor.hoair.cn/ho-ecsm/ruoyi-file-$(name):$(TAG)
else
	@echo                 ---- WARN! ----
	@echo  Please enter the name of the service to run!!
	@echo  -------------------------------------------------------
endif

