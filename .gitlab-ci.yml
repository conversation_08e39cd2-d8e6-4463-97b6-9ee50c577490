image: maven:3.6.3-jdk-8

stages:
  - build
  - push
  
# 将 Nexus 仓库地址添加到 settings.xml 中
before_script:
  - echo '<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0"
              xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
              xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0
                                  http://maven.apache.org/xsd/settings-1.0.0.xsd">
                <servers>
                    <server>
                        <id>maven-release</id>
                        <username>deploy</username>
                        <password>deploy</password>
                </server>
                </servers>

              <mirrors>
                  <mirror>
                      <id>nexus-repo-mirror</id>
                  <mirrorOf>*</mirrorOf>
                  <url>http://nexus.juneyaoair.com:8081/repository/maven-public/</url>
                  </mirror>
              </mirrors>
            </settings>'> /root/.m2/settings.xml

# 运行 Maven 编译命令
tags_trigger_build:
  stage: build
  script:
    - mvn clean package -Dmaven.test.skip=true -P prd
  artifacts:
    paths:
      - ruoyi-gateway/target/ruoyi-gateway.jar
      - ruoyi-auth/target/ruoyi-auth.jar
      - ruoyi-modules/ruoyi-system/target/ruoyi-modules-system.jar
    name: "${CI_COMMIT_TAG}"
  only:
    - tags
  tags:
    - build

tags_trigger_push:
  stage: push
  script:
    - echo "deploy...${CI_COMMIT_TAG}"
    - curl -u deploy:deploy --upload-file ruoyi-gateway/target/ruoyi-gateway.jar http://mirror.juneyaoair.com:8081/repository/p-release/${CI_PROJECT_NAME}/${CI_COMMIT_TAG}/
    - curl -u deploy:deploy --upload-file ruoyi-auth/target/ruoyi-auth.jar http://mirror.juneyaoair.com:8081/repository/p-release/${CI_PROJECT_NAME}/${CI_COMMIT_TAG}/
    - curl -u deploy:deploy --upload-file ruoyi-modules/ruoyi-system/target/ruoyi-modules-system.jar http://mirror.juneyaoair.com:8081/repository/p-release/${CI_PROJECT_NAME}/${CI_COMMIT_TAG}/
  allow_failure: false
  dependencies:
    - tags_trigger_build
  only:
    - tags
  tags:
    - build

  
