package com.ruoyi.auth.service;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.auth.util.AirUuidUtil;
import com.ruoyi.auth.util.DateUtils;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.redis.service.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;


/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class PhoneMsgService {

    private static String BATCH_SMS_MESSAGE = "/smsMessage/batchSmsMessage";
    private static String TOKEN_URL = "/auth/getToken";
    private static final String HOUMS_TOKEN = "workline:call:houmsToken";

    @Value("${phoneMsgUrl:https://hopush.juneyaoair.com}")
    private String houmsUrl;


    @Value("${msgTemplateCode:SMS000291}")
    private String templateCode;

    @Value("${channelCode:QD000020}")
    private String channelCode;

    @Value("${channelSecret:IAIJYBTGLTZ0KDHFYCJ1YD9SILL1ULYM3U6D6GQXEBZRN9N6KSIEWPO9UMANUQYP}")
    private String channelSecret;



    @Autowired
    private RedisService redisService;


    public void batchSmsMessage(String phone,int code,String system) {
        JSONObject object = new JSONObject();
        // 请求唯一标识 时间戳加8位UUID
        Date now = new Date();
        String bizNo = DateUtils.format(now, DateUtils.DATE_TIME_CONTINUITY) + AirUuidUtil.randomUUID8();
        object.put("bizNo", bizNo);
        object.put("version", "1.0");
        JSONObject request = new JSONObject();
        // 发送通道
        request.put("aisleNumber", "HOYXTYSMS10305");
        // 短信区域id 应用于国际短信，国内短信可为空
        request.put("areaId", "");
        // 批次ID 随机不可重复
        request.put("batchId", AirUuidUtil.randomUUID8());
        // 最晚发送时间 格式：yyyy-MM-dd HH:mm:ss
        // 短信模板编号
        request.put("smsTemplateCode", templateCode);
        // 任务ID建议使用（短信模板编号_yyyyMMdd）格式
        request.put("taskId", String.format("SMS000291_%s", DateUtils.format(now, DateUtils.DATE_CONTINUITY)));
        object.put("request", request);
        // 发送人员信息清单 单次：最多1000个
        JSONArray userList = new JSONArray(1);
        JSONObject user = new JSONObject();
        // 消息额外参数（用于消息模板内容替换） 格式：Map<String, String>
        user.put("phone", phone);
        Map<String, Object> map = new HashMap<>();
        map.put("verifyCode", code);
        map.put("system", system);
        user.put("extras", map);
        //消息id
        user.put("messageId", phone + "_" + AirUuidUtil.randomUUID8());
        userList.add(user);
        request.put("userList", userList);
        String messageUrl = houmsUrl + BATCH_SMS_MESSAGE;
        HttpRequest post = HttpUtil.createPost(messageUrl);
        post.body(object.toJSONString());
        // 获取token
        post.header("token", this.getHoumsToken());
        String response = post.execute().body();
        JSONObject resJson = JSON.parseObject(response);
        log.debug("短信平台发送短信url:【{}】,body:【{}】,结果:【{}】", messageUrl, object.toJSONString(), resJson);
        if (resJson.getInteger("resultCode") != 1001) {
            log.error("短信平台调用发送短信失败:{}", resJson.getString("errorMsg"));
            throw new ServiceException(resJson.getString("errorMsg"));
        }
        log.info("短信平台发送短信成功:{}", phone);


    }


    /**
     * 获取消息平台token
     *
     * @return token
     */
    private String getHoumsToken() {
        // 获取redis中缓存数据
        Object tokenObj = redisService.get(HOUMS_TOKEN);
        if (tokenObj != null) {
            return tokenObj.toString();
        }
        // 如果为空获取新的token
        String tokenUrl = houmsUrl + TOKEN_URL;
        JSONObject body = new JSONObject();
        // 时间戳加8位UUID
        String bizNo = DateUtils.format(new Date(), DateUtils.DATE_TIME_CONTINUITY) + AirUuidUtil.randomUUID8();
        body.put("bizNo", bizNo);
        body.put("version", "1.0");
        JSONObject reqJson = new JSONObject();
        reqJson.put("channelCode", channelCode);
        reqJson.put("channelSecret", channelSecret);
        body.put("request", reqJson);
        log.debug("获取消息平台token地址:【{}】,body:【{}】", tokenUrl, body);
        String res = HttpUtil.post(tokenUrl, body.toJSONString());
        log.debug("获取消息平台token返回数据bizNo:【{}】,结果:【{}】", bizNo, res);
        JSONObject resJson = JSON.parseObject(res);
        if (resJson.getInteger("resultCode") != 1001) {
            throw new ServiceException(resJson.getString("errorMsg"));
        }
        JSONObject result = resJson.getJSONObject("result");
        int expiresIn = result.getInteger("expiresIn") - 60;
        redisService.set(HOUMS_TOKEN, result.getString("token"), (long) expiresIn);
        return result.getString("token");
    }


}
