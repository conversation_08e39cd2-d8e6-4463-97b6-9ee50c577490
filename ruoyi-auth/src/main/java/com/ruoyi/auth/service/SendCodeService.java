package com.ruoyi.auth.service;

import com.ruoyi.auth.aggr.WeChatCodeAggr;
import com.ruoyi.auth.content.LoginConfig;
import com.ruoyi.auth.content.SystemConfig;
import com.ruoyi.auth.form.LoginBody;
import com.ruoyi.auth.util.AirOkHttpClientUtil;
import com.ruoyi.auth.util.CodeUtil;
import com.ruoyi.auth.util.LdapUtil;
import com.ruoyi.common.core.constant.Constants;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.constant.UserConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.enums.UserStatus;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.RemoteUserService;
import com.ruoyi.system.api.domain.SysUser;
import com.ruoyi.system.api.model.LoginUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.Map;
import java.util.Optional;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
@Service
public class SendCodeService {

    @Value(value = "${wechat.send.url:https://micro-api2.juneyaoair.com/service-datacenter/wechat/sendTextMessageByAdBatch}")
    private String url;

    @Value(value = "${wechat.send.template:【研发流水线】您的验证码是{%s}，2分钟内有效，为了保障信息安全，请勿告诉他人，如非本人操作，请忽略。}")
    private String template;

    @Value(value = "${phoneCodeSwitch:false}")
    private Boolean phoneCodeSwitch;



    @Value(value = "${adminAccount:guoxiaohui}")
    private String adminAccount;


    @Autowired
    private RemoteUserService remoteUserService;


    @Autowired
    private SysRecordLogService recordLogService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private PhoneMsgService phoneMsgService;

    @Autowired
    private LoginConfig loginConfig;


    @Autowired
    private SysPasswordService passwordService;

    private static final String PHONE_NUMBER_PATTERN = "^1[3-9]\\d{9}$";

    private static final Pattern pattern = Pattern.compile(PHONE_NUMBER_PATTERN);
    /**
     * 超级用户登录名
     */
    public static final String ADMIN_LOGIN = "ecsmaster";
    @Value("${ecs.ldapUrl:ldap://juneyaoair.com:389}")
    private String LDAP_URL;
    /**
     * 发送验证码
     * @param form 登录信息
     * @return
     */
    public void sendCode(LoginBody form, HttpServletRequest request) {
        //拦截是否在有效期内可以发送，一分钟内只能发送一次
        checkIfSendCode(form.getUsername());

////        验证账号密码正确性
        LoginUser loginUser = checkPassword(form.getUsername(), form.getPassword());
//        获取用户信息
        if (loginUser == null || loginUser.getSysUser() == null){
            throw new ServiceException("该账号不存在，请联系管理人员！");
        }
        Integer code = null;
        //给管理员发个企业微信
        SystemConfig systemConfig = getSendAdminAccount(request.getHeader("Appid"));
        if (systemConfig == null){
            throw new ServiceException(String.format("抱歉，该系统id:%s未配置，请联系管理员",request.getHeader("Appid")));
        }
        String sendAdminAccount = systemConfig.getAccount();
        String sendAdminTemplate = systemConfig.getTemplate();
        String sendAdminName = systemConfig.getName();
        if(ADMIN_LOGIN.equals(form.getUsername())){
            code = sendWeChatCode(sendAdminTemplate,sendAdminAccount);
        }else {
            //验证发送类型
            if(CodeUtil.ifSendPhoneCode(form.getUsername(),phoneCodeSwitch)){
                // 发送手机验证码
                try {
                    pattern.matcher(loginUser.getSysUser().getPhonenumber()).matches();
                    code = randomCode();
                    phoneMsgService.batchSmsMessage(loginUser.getSysUser().getPhonenumber(),code, sendAdminName);
                } catch (Exception e) {
                    throw new ServiceException("该账号手机号出现问题，请联系管理人员！");
                }
            }else {
                //发送企业微信验证码
                code = sendWeChatCode(sendAdminTemplate, form.getUsername());
            }
        }
        // 验证码存入redis 有效两分钟，可替换
        redisService.set("workline:login:code:" + form.getUsername(), String.valueOf(code), 120);
        // 发送有效期存入redis 有效期1分钟
        redisService.set("workline:login:check:" + form.getUsername(), String.valueOf(System.currentTimeMillis()), 60);
    }

    public SystemConfig getSendAdminAccount( String appId) {
        // 在系统列表中查找
        Optional<SystemConfig> optionalSystemConfig = loginConfig.getAdminList().stream()
                .filter(systemConfig -> systemConfig.getAppId().equals(appId))
                .findFirst();
        return optionalSystemConfig.orElse(null);

    }

    /**
     * 获取验证码
     * @return
     */
    private static int randomCode(){
        return (int)((Math.random()*9+1)*100000);
    }


    /**
     * 限制每人一分钟只能发送一次验证码
     */
    public void checkIfSendCode(String account) {
        Object obj = redisService.get("workline:login:check:" + account);
        if (obj != null){
            throw new ServiceException("一分钟只能发送一次验证码");
        }
    }

    /**
     * 验证账号密码
     */
    public LoginUser checkPassword(String username, String password)
    {
        // 用户名或密码为空 错误
        if (StringUtils.isAnyBlank(username, password))
        {
            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "用户/密码必须填写");
            throw new ServiceException("用户/密码必须填写");
        }
        // 密码如果不在指定范围内 错误
        if (password.length() < UserConstants.PASSWORD_MIN_LENGTH
                || password.length() > UserConstants.PASSWORD_MAX_LENGTH)
        {
            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "用户密码不在指定范围");
            throw new ServiceException("用户密码不在指定范围");
        }
        // 用户名不在指定范围内 错误
        if (username.length() < UserConstants.USERNAME_MIN_LENGTH
                || username.length() > UserConstants.USERNAME_MAX_LENGTH)
        {
            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "用户名不在指定范围");
            throw new ServiceException("用户名不在指定范围");
        }

        String DisplayName = username;
        Boolean isAdminLogin = ADMIN_LOGIN.equals(username);
        if(!isAdminLogin) {
            Map<String, String> ldapRe = LdapUtil.checkUserByLdapInSendCode(username, password, LDAP_URL);
            DisplayName = ldapRe.get("DisplayName");
        }

        // 查询用户信息
        R<LoginUser> userResult = remoteUserService.getUserInfo(username, SecurityConstants.INNER);
        if (R.FAIL == userResult.getCode() || StringUtils.isNull(userResult) || StringUtils.isNull(userResult.getData()))
        {
            // 注册用户信息
            SysUser sysUser = new SysUser();
            sysUser.setUserName(username);
            sysUser.setNickName(DisplayName);
            sysUser.setPassword(SecurityUtils.encryptPassword(username+"8888"));
            R<?> registerResult = remoteUserService.registerUserInfo(sysUser, SecurityConstants.INNER);

            if (R.FAIL == registerResult.getCode())
            {
                throw new ServiceException(registerResult.getMsg());
            }
            recordLogService.recordLogininfor(username, Constants.REGISTER, "注册成功");

            //重新获取用户信息
            userResult = remoteUserService.getUserInfo(username, SecurityConstants.INNER);
        }

        LoginUser userInfo = userResult.getData();
        SysUser user = userResult.getData().getSysUser();
        if (UserStatus.DELETED.getCode().equals(user.getDelFlag()))
        {
            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "对不起，您的账号已被删除");
            throw new ServiceException("对不起，您的账号：" + username + " 已被删除");
        }
        if (UserStatus.DISABLE.getCode().equals(user.getStatus()))
        {
            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "用户已停用，请联系管理员");
            throw new ServiceException("对不起，您的账号：" + username + " 已停用");
        }

        if(isAdminLogin) {
            //使用LDAP代替数据库密码验证，
            //只有在，超级用户登录情况下，开启数据库密码认证
            passwordService.validate(user, password);
        }

        return userInfo;
    }


    public Integer sendWeChatCode(String msg,String userName) {
        AirOkHttpClientUtil airOkHttpClientUtil = AirOkHttpClientUtil.create(url);
        try {
            //构造发送信息
            WeChatCodeAggr weChatCodeAggr = new WeChatCodeAggr(userName,msg);
            airOkHttpClientUtil.post(weChatCodeAggr.getBody());
            return weChatCodeAggr.getCode();
        } catch (IOException e) {
            throw new ServiceException(e.getMessage());
        }
    }



}
