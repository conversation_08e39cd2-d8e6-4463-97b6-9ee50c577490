package com.ruoyi.auth.service;

import com.alibaba.fastjson2.JSON;
import com.haitai.response.ResultCode;
import com.ruoyi.auth.content.LoginConfig;
import com.ruoyi.auth.form.LoginBody;
import com.ruoyi.auth.util.LdapUtil;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.sac.service.SignService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import com.ruoyi.common.core.constant.Constants;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.constant.UserConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.enums.UserStatus;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.RemoteUserService;
import com.ruoyi.system.api.domain.SysUser;
import com.ruoyi.system.api.model.LoginUser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * 登录校验方法
 * 通过 Spring Cloud 原生注解 @RefreshScope 实现配置自动更新
 * <AUTHOR>
 */
@Component
@RefreshScope
public class SysLoginService
{
    private static final Logger log = LoggerFactory.getLogger(SysLoginService.class);

    @Autowired
    private RemoteUserService remoteUserService;

    @Autowired
    private SysPasswordService passwordService;

    @Autowired
    private SysRecordLogService recordLogService;


    @Autowired
    private RedisService redisService;


    @Autowired
    private LoginConfig loginConfig;

    @Autowired(required = false)
    private SignService signService;

    /**
     * 超级用户登录名
     */
    public static final String ADMIN_LOGIN = "ecsmaster";

    @Value("${ecs.ldapUrl:ldap://juneyaoair.com:389}")
    private String LDAP_URL;

    /**
     * 登录
     */
    public LoginUser login(String username, String password)
    {
        // 用户名或密码为空 错误
        if (StringUtils.isAnyBlank(username, password))
        {
            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "用户/密码必须填写");
            throw new ServiceException("用户/密码必须填写");
        }
        // 密码如果不在指定范围内 错误
        if (password.length() < UserConstants.PASSWORD_MIN_LENGTH
                || password.length() > UserConstants.PASSWORD_MAX_LENGTH)
        {
            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "用户密码不在指定范围");
            throw new ServiceException("用户密码不在指定范围");
        }
        // 用户名不在指定范围内 错误
        if (username.length() < UserConstants.USERNAME_MIN_LENGTH
                || username.length() > UserConstants.USERNAME_MAX_LENGTH)
        {
            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "用户名不在指定范围");
            throw new ServiceException("用户名不在指定范围");
        }

        String DisplayName = username;
        Boolean isAdminLogin = ADMIN_LOGIN.equals(username);
        if(!isAdminLogin) {
            Map<String, String> ldapRe = LdapUtil.checkUserByLdap(username, password, LDAP_URL);
            DisplayName = ldapRe.get("DisplayName");
        }

        // 查询用户信息
        R<LoginUser> userResult = remoteUserService.getUserInfo(username, SecurityConstants.INNER);
        if (R.FAIL == userResult.getCode() || StringUtils.isNull(userResult) || StringUtils.isNull(userResult.getData()))
        {
            // 注册用户信息
            SysUser sysUser = new SysUser();
            sysUser.setUserName(username);
            sysUser.setNickName(DisplayName);
            sysUser.setPassword(SecurityUtils.encryptPassword(username+"8888"));
            R<?> registerResult = remoteUserService.registerUserInfo(sysUser, SecurityConstants.INNER);

            if (R.FAIL == registerResult.getCode())
            {
                throw new ServiceException(registerResult.getMsg());
            }
            recordLogService.recordLogininfor(username, Constants.REGISTER, "注册成功");

            //重新获取用户信息
            userResult = remoteUserService.getUserInfo(username, SecurityConstants.INNER);
        }

        LoginUser userInfo = userResult.getData();
        SysUser user = userResult.getData().getSysUser();
        if (UserStatus.DELETED.getCode().equals(user.getDelFlag()))
        {
            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "对不起，您的账号已被删除");
            throw new ServiceException("对不起，您的账号：" + username + " 已被删除");
        }
        if (UserStatus.DISABLE.getCode().equals(user.getStatus()))
        {
            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "用户已停用，请联系管理员");
            throw new ServiceException("对不起，您的账号：" + username + " 已停用");
        }

        if(isAdminLogin) {
            //使用LDAP代替数据库密码验证，
            //只有在，超级用户登录情况下，开启数据库密码认证
            passwordService.validate(user, password);
        }

        recordLogService.recordLogininfor(user.getUserId(), username, Constants.LOGIN_SUCCESS, "登录成功");
        return userInfo;
    }

    public void logout(String loginName)
    {
        recordLogService.recordLogininfor(loginName, Constants.LOGOUT, "退出成功");
    }

    /**
     * 注册
     */
    public void register(String username, String password)
    {
        // 用户名或密码为空 错误
        if (StringUtils.isAnyBlank(username, password))
        {
            throw new ServiceException("用户/密码必须填写");
        }
        if (username.length() < UserConstants.USERNAME_MIN_LENGTH
                || username.length() > UserConstants.USERNAME_MAX_LENGTH)
        {
            throw new ServiceException("账户长度必须在2到20个字符之间");
        }
        if (password.length() < UserConstants.PASSWORD_MIN_LENGTH
                || password.length() > UserConstants.PASSWORD_MAX_LENGTH)
        {
            throw new ServiceException("密码长度必须在5到20个字符之间");
        }

        // 注册用户信息
        SysUser sysUser = new SysUser();
        sysUser.setUserName(username);
        sysUser.setNickName(username);
        sysUser.setPassword(SecurityUtils.encryptPassword(password));
        R<?> registerResult = remoteUserService.registerUserInfo(sysUser, SecurityConstants.INNER);

        if (R.FAIL == registerResult.getCode())
        {
            throw new ServiceException(registerResult.getMsg());
        }
        recordLogService.recordLogininfor(username, Constants.REGISTER, "注册成功");
    }

    /**
     * 用户认证
     * @param username 用户名
     * @param password 密码
     * @param code 验证码
     * @param random UKey随机数（可为空）
     * @param p7SignData UKey签名数据（可为空）
     * @return 登录用户信息
     */
    public LoginUser authenticateUser(String username, String password, String code, String random, String p7SignData) {
        if ("N".equals(loginConfig.getLoginCheck())) {
            // 不需要验证码
            return login(username, password);
        }
        
        // 判断是否为管理员登录
        Boolean isAdminLogin = ADMIN_LOGIN.equals(username);
        
        // 如果是管理员账号，优先检查UKey认证
        if (isAdminLogin && StringUtils.isNotBlank(random) && StringUtils.isNotBlank(p7SignData)) {
            try {
                if (signService == null) {
                    log.warn("签名服务未启用，跳过UKey验证");
                    return login(username, password);
                }

                // 验证P7签名
                ResultCode<Boolean> verifyResult = signService.detachedVerify(random, p7SignData);
                if (verifyResult.getCode() != 0 || !verifyResult.getData()) {
                    log.info("UKey签名验证失败 random={} p7SignData={} verifyResult={}", random, p7SignData, JSON.toJSONString(verifyResult));
                    recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "UKey签名验证失败");
                    throw new ServiceException("UKey签名验证失败");
                } else {
                    log.info("UKey验证成功 random={} p7SignData={}", random, p7SignData);
                }
                
                recordLogService.recordLogininfor(username, Constants.LOGIN_SUCCESS, "UKey验证成功");
                // 验证成功，继续普通登录流程
                return login(username, password);
            } catch (Exception e) {
                recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "UKey验证处理异常：" + e.getMessage());
                throw new ServiceException("UKey验证处理异常：" + e.getMessage());
            }
        }
        
        // 常规验证码验证
        Object obj = redisService.get("workline:login:code:" + username);
        if (obj != null && obj.toString().equals(code)) {
            redisService.deleteObject("workline:login:code:" + username);
            return login(username, password);
        }
        throw new ServiceException("验证码错误");
    }

    /**
     * 用户认证（兼容原有接口）
     * @param username 用户名
     * @param password 密码
     * @param code 验证码
     * @return 登录用户信息
     */
    public LoginUser authenticateUser(String username, String password, String code) {
        return authenticateUser(username, password, code, null, null);
    }



}
