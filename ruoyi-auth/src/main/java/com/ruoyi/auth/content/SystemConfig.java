package com.ruoyi.auth.content;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SystemConfig {
    @ApiModelProperty(value = "系统id")
    private String appId;

    @ApiModelProperty(value = "管理员账号")
    private String account;

    private String template;

    private String name;



    public String setString() {
        return "System(" +
                "appId='" + appId + '\'' +
                ", account='" + account + '\'' +
                ')';
    }
}
