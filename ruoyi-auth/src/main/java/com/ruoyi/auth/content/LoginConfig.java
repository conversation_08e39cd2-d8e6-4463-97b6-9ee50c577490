package com.ruoyi.auth.content;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
@ConfigurationProperties(prefix = "login-config")
public class LoginConfig {
    private String loginCheck;
    private List<SystemConfig> adminList;

    public String getLoginCheck() {
        return loginCheck;
    }

    public void setLoginCheck(String loginCheck) {
        this.loginCheck = loginCheck;
    }

    public List<SystemConfig> getAdminList() {
        return adminList;
    }

    public void setAdminList(List<SystemConfig> adminList) {
        this.adminList = adminList;
    }
}
