package com.ruoyi.auth.util;

import com.ruoyi.common.core.exception.ServiceException;

import javax.naming.AuthenticationException;
import javax.naming.Context;
import javax.naming.NamingEnumeration;
import javax.naming.directory.*;
import java.util.HashMap;
import java.util.Hashtable;
import java.util.Map;
import java.util.Optional;

/**
 * LdapUtil Class
 *
 * <AUTHOR>
 * @since 2022/9/1
 */
public class LdapUtil {

    private LdapUtil() {
        throw new IllegalStateException("LdapUtil class");
    }

    private static final String[] initAttrs = {
            "mail",
            "homePhone",
            "telephoneNumber",
            "description",
            "mobile",
            "DisplayName",
            "Department",
            "userPrincipalName"};

    @SuppressWarnings("squid:S1149")
    public static Map<String, String> checkUserByLdap(String username, String password, String ldapUrl) {
        Hashtable<String, String> env = new Hashtable<>();
        env.put(Context.SECURITY_AUTHENTICATION, "simple");
        env.put(Context.SECURITY_PRINCIPAL, username + "@juneyaoair.com");
        env.put(Context.SECURITY_CREDENTIALS, password);
        env.put(Context.INITIAL_CONTEXT_FACTORY, "com.sun.jndi.ldap.LdapCtxFactory");
        env.put(Context.PROVIDER_URL, ldapUrl);
        DirContext ctx = null;
        try {
            ctx = new InitialDirContext(env);
            SearchControls searchControls = new SearchControls();
            searchControls.setSearchScope(SearchControls.SUBTREE_SCOPE);
            searchControls.setReturningAttributes(initAttrs);
            NamingEnumeration<SearchResult> answer =
                    ctx.search("DC=juneyaoair,DC=com", "sAMAccountName=" + username, searchControls);
            if (!answer.hasMoreElements()) {
                throw new ServiceException("非法域账号");
            }
            SearchResult sr = answer.next();
            if (sr == null) {
                throw new ServiceException("非法域账号");
            }
            Attributes attrs = sr.getAttributes();
            Map<String, String> map = new HashMap<>();

            for (String key : initAttrs) {
                Optional<Attribute> optional = Optional.ofNullable(attrs.get(key));
                if (optional.isPresent()) {
                    map.put(key, optional.get().get().toString());
                }
            }
            map.put("Department", map.getOrDefault("Department",
                    map.getOrDefault("description", "未知部门")));
            map.put("mobile", map.getOrDefault("homePhone",
                    map.getOrDefault("telephoneNumber",
                            map.getOrDefault("mobile", ""))));
            return map;
        } catch (AuthenticationException e) {
            throw new ServiceException("用户登录失败，密码错误");
        } catch (javax.naming.CommunicationException e) {
            throw new ServiceException("用户登录失败，AD域连接失败");
        } catch (Exception e) {
            throw new ServiceException("身份验证未知异常:");
        } finally {
            if (null != ctx) {
                try {
                    ctx.close();
                } catch (Exception e) {

                }
            }
        }

    }
    public static Map<String, String> checkUserByLdapInSendCode(String username, String password, String ldapUrl) {
        Hashtable<String, String> env = new Hashtable<>();
        env.put(Context.SECURITY_AUTHENTICATION, "simple");
        env.put(Context.SECURITY_PRINCIPAL, username + "@juneyaoair.com");
        env.put(Context.SECURITY_CREDENTIALS, password);
        env.put(Context.INITIAL_CONTEXT_FACTORY, "com.sun.jndi.ldap.LdapCtxFactory");
        env.put(Context.PROVIDER_URL, ldapUrl);
        DirContext ctx = null;
        try {
            ctx = new InitialDirContext(env);
            SearchControls searchControls = new SearchControls();
            searchControls.setSearchScope(SearchControls.SUBTREE_SCOPE);
            searchControls.setReturningAttributes(initAttrs);
            NamingEnumeration<SearchResult> answer =
                    ctx.search("DC=juneyaoair,DC=com", "sAMAccountName=" + username, searchControls);
            if (!answer.hasMoreElements()) {
                throw new ServiceException("非法域账号");
            }
            SearchResult sr = answer.next();
            if (sr == null) {
                throw new ServiceException("非法域账号");
            }
            Attributes attrs = sr.getAttributes();
            Map<String, String> map = new HashMap<>();

            for (String key : initAttrs) {
                Optional<Attribute> optional = Optional.ofNullable(attrs.get(key));
                if (optional.isPresent()) {
                    map.put(key, optional.get().get().toString());
                }
            }
            map.put("Department", map.getOrDefault("Department",
                    map.getOrDefault("description", "未知部门")));
            map.put("mobile", map.getOrDefault("homePhone",
                    map.getOrDefault("telephoneNumber",
                            map.getOrDefault("mobile", ""))));
            return map;
        } catch (AuthenticationException e) {
            throw new ServiceException("发送验证码失败，账号密码错误");
        } catch (javax.naming.CommunicationException e) {
            throw new ServiceException("发送验证码失败，AD域连接失败");
        } catch (Exception e) {
            throw new ServiceException("发送验证码失败:身份验证异常");
        } finally {
            if (null != ctx) {
                try {
                    ctx.close();
                } catch (Exception e) {

                }
            }
        }

    }

}
