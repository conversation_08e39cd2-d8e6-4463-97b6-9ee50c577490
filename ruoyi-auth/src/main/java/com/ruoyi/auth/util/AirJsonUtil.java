/*
 * Copyright ®2018 juneyaoair Group.
 *
 *
 *
 */

package com.ruoyi.auth.util;

import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.TimeZone;

/**
 * 封装json工具，可以统一json。如需要复杂解析，例如自定义解析器可进行扩展
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2018年2月24日
 */
@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class AirJsonUtil {

    /**
     * 统一json转换，避免子类使用各种json工具导致错误或难以维护
     *
     * @return
     */
    protected static ObjectMapper getObjectMapper() {
        return new ObjectMapper();
    }

    /**
     * 字符串格式json转换为实体对象。
     * <div>
     * 	<span style = "color:red;">注意</span><br/>
     * 	默认转换时，如果json字符串中属性字段多余 实体对象属性字段，转换时不会报错（多余部分不转换）。如需处理，使用
     *  	<code>{@link #parseObject(String, Class, DeserializationFeature)}</code><br/>
     *  如果无法转换或转换异常，返回null
     * </div>
     *
     * @param responseStr 返回报文字符串格式
     * @param clazz
     * @return
     */
    public static <T> T parseObject(String responseStr, Class<T> clazz) {
        return parseObject(responseStr, clazz, null);
    }

    /**
     * @see AirJsonUtil
     * @see #parseObject
     */
    public static <T> T parseObject(String responseStr, Class<T> clazz, DeserializationFeature config) {
        ObjectMapper mapper = getObjectMapper();
        //json 设置时区为东8区
        mapper.setTimeZone(TimeZone.getTimeZone("GMT+8"));
        if (config == null) {
            mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        } else {
            mapper.configure(config, false);
        }

        try {
            return mapper.readValue(responseStr, clazz);
        } catch (Exception e) {
            log.error("json解析异常，报文无法解析成对象。错误信息:" + e.getMessage());
            log.error("json解析异常，报文无法解析成对象。请求报文:" + responseStr);
            log.error("json解析异常，报文无法解析成对象。转换对象:" + clazz.getName());
            return null;
        }
    }

    /**
     * @see AirJsonUtil
     * @see #parseObject
     */
    public static <T> T parseObject(String responseStr, DeserializationFeature config, Class<?> collectionClass, Class<?>... elementClasses) {
        ObjectMapper mapper = getObjectMapper();
        JavaType javaType = mapper.getTypeFactory().constructParametricType(collectionClass, elementClasses);

        //json 设置时区为东8区
        mapper.setTimeZone(TimeZone.getTimeZone("GMT+8"));
        if (config == null) {
            mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        } else {
            mapper.configure(config, false);
        }
        try {
            return mapper.readValue(responseStr, javaType);
        } catch (Exception e) {
            log.error("json解析异常，报文无法解析成对象。错误信息:" + e.getMessage());
            log.error("json解析异常，报文无法解析成对象。请求报文:" + responseStr);
            log.error("json解析异常，报文无法解析成对象。转换对象:" + javaType.toString());
            return null;
        }
    }

    /**
     * 把对象转换为json字符串形式，无法转换返回null
     *
     * @param obj     需要转换的对象
     * @param include 转换规则,例如Include.NON_NULL,obj对象为空的属性不进行转换。详情 see {@link Include}
     * @return json格式的字符串表示形式，无法转换返回null
     */
    public static String toJSONString(Object obj, Include include) {
        ObjectMapper mapper = getObjectMapper();
        if (include != null) {
            mapper.setSerializationInclusion(include);
        }
        try {
            return mapper.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            log.error("json解析异常，对象无法解析成报文。错误信息:" + e.getMessage());
            log.error("json解析异常，对象无法解析成报文。转换对象:" + obj);
            return null;
        }
    }

    /**
     * 把对象转换为json字符串形式，无法转换返回null。<br/>默认转换规则：属性为null的不进行转换。需要转换只用<code>toJSONString(obj,null)</code>
     *
     * @param obj 需要转换的对象
     * @return json格式的字符串表示形式，无法转换返回null
     */
    public static String toJSONString(Object obj) {
        return toJSONString(obj, Include.NON_NULL);
    }
}
