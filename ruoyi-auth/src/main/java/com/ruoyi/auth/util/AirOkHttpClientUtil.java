
package com.ruoyi.auth.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.auth.util.AirJsonUtil;
import com.ruoyi.auth.util.CopyStreamUtils;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.io.FilenameUtils;

import java.io.*;
import java.util.Arrays;
import java.util.Date;
import java.util.Iterator;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.function.Function;


@Slf4j
public class AirOkHttpClientUtil {

    private static final MediaType XLSX = MediaType.parse("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
    private static final MediaType XLS = MediaType.parse("application/vnd.ms-excel");
    private static final MediaType IMAGE = MediaType.parse("image/*");
    private static final MediaType PDF = MediaType.parse("application/pdf");
    private static final MediaType DOC = MediaType.parse("application/msword");
    private static final MediaType DOCX = MediaType.parse("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
    private static final MediaType JSON = MediaType.parse("application/json; charset=utf-8");
    private static final MediaType MULT = MediaType.parse("application/octet-stream");

    static final Function<String, MediaType> getType = filename -> {
        switch (filename) {
            case "xlsx":
                return XLSX;
            case "xls":
                return XLS;
            case "jpg":
                return IMAGE;
            case "gif":
                return IMAGE;
            case "png":
                return IMAGE;
            case "jpeg":
                return IMAGE;
            case "pdf":
                return PDF;
            case "doc":
                return DOC;
            case "docx":
                return DOCX;
        }
        return null;
    };


    private static int globalTimeout = 60;

    private final String url;
    private String body;

    private String rspString;

    private Map<String, String> para;

    private Consumer<String> logger = (msg) -> {

    };

    private int timeout = -1;
    private Headers headers = null;

    private AirOkHttpClientUtil(String url) {
        this.url = url;
    }

    public static void setGlobalTimeout(int timeout) {
        globalTimeout = timeout;
    }

    public static AirOkHttpClientUtil create(String url) {

        return new AirOkHttpClientUtil(url);
    }

    public AirOkHttpClientUtil timeout(int timeout) {
        this.timeout = timeout;
        if (this.timeout < 1) {
            this.timeout = globalTimeout;
        }
        return this;
    }

    /**
     * json post请求
     *
     * @param body 请求json
     * @return 本对象
     * @throws IOException
     */
    public AirOkHttpClientUtil post(String body) throws IOException {
        this.body = body;
        RequestBody requestBody = RequestBody.create(JSON, body);
        proceedRequest(requestBody);
        return this;
    }

    /**
     * post 表单请求
     *
     * @param para 表单参数
     * @return 本对象
     * @throws IOException
     */
    public AirOkHttpClientUtil postForm(Map<String, Object> para) throws IOException {
        if (para == null || para.size() == 0)
            return this;

        FormBody.Builder builder = new FormBody.Builder();

        for (Map.Entry<String, Object> entry : para.entrySet()) {
            if (entry != null && entry.getKey() != null && entry.getValue() != null) {
                builder.add(entry.getKey(), entry.getValue().toString());
            }
        }
        RequestBody formBody = builder.build();
        this.body = AirJsonUtil.toJSONString(para);
        proceedRequest(formBody);
        return this;
    }

    /**
     * post 文件上传
     *
     * @param para 表单参数
     * @return 本对象
     * @throws IOException
     */
    public AirOkHttpClientUtil postFormMult(Map<String, Object> para, String fileKey, String fileName, InputStream is) throws Exception {
        if (para == null || para.size() == 0)
            return this;
        MultipartBody.Builder builder = new MultipartBody.Builder();
        builder.setType(MultipartBody.FORM);
        for (Map.Entry<String, Object> entry : para.entrySet()) {
            if (entry != null && entry.getKey() != null && entry.getValue() != null) {
                builder.addFormDataPart(entry.getKey(), entry.getValue().toString());
            }
        }
        RequestBody fileBody = RequestBody.create(MULT, CopyStreamUtils.readStream(is));
        builder.addFormDataPart(fileKey, fileName, fileBody);
        RequestBody formBody = builder.build();
        this.body = AirJsonUtil.toJSONString(para);
        proceedRequest(formBody);
        return this;
    }

    public AirOkHttpClientUtil postFormMult(Map<String, Object> para, String fileKey, String fileName, String content) throws Exception {
        ByteArrayInputStream inputStream = new ByteArrayInputStream(content.getBytes());
        this.postFormMult(para, fileKey, fileName, inputStream);
        return this;
    }


    public void download(OutputStream os) throws IOException {
        // 如果没有设置超时，使用全局的超时时间。
        if (this.timeout < 1) {
            this.timeout = globalTimeout;
        }
        if (headers == null) {
            headers = new Headers.Builder().build();
        }
        Request request = new Request.Builder()
                .url(url)
                .build();
        OkHttpClient client = new OkHttpClient.Builder()
                .connectTimeout(120, TimeUnit.SECONDS)
                .readTimeout(this.timeout, TimeUnit.SECONDS)
                .build();
        Response rsp = client.newCall(request).execute();
        os.write(rsp.body().bytes());
        os.flush();
        os.close();
    }

    public AirOkHttpClientUtil upload(String key, File[] files) throws IOException {
        MultipartBody.Builder requestBody = new MultipartBody.Builder().setType(MultipartBody.FORM);

        Arrays.stream(files).forEach(file -> {
            if (file != null) {
                // MediaType.parse() 里面是上传的文件类型。
                RequestBody body = RequestBody.create(getType.apply(FilenameUtils.getExtension(file.getName())), file);
                String filename = file.getName();
                // 参数分别为， 请求key ，文件名称 ， RequestBody
                requestBody.addFormDataPart(key, filename, body);
            }
        });
        RequestBody formBody = requestBody.build();
        proceedRequest(formBody);
        return this;
    }

    /**
     * 执行请求结果
     *
     * @param requestBody 请求参数
     * @return 本对象
     * @throws IOException
     */
    private AirOkHttpClientUtil proceedRequest(RequestBody requestBody) throws IOException {
        // 如果没有设置超时，使用全局的超时时间。
        if (this.timeout < 1) {
            this.timeout = globalTimeout;
        }
        if (headers == null) {
            headers = new Headers.Builder().build();
        }
        Request request = new Request.Builder()
                .url(url)
                .headers(headers)
                .post(requestBody)
                .build();
        OkHttpClient client = new OkHttpClient.Builder()
                .connectTimeout(120, TimeUnit.SECONDS)
                .readTimeout(this.timeout, TimeUnit.SECONDS)
                .build();
        long start = System.currentTimeMillis();
        String uuid = AirUuidUtil.randomUUID8();
        logger.accept(String.format("POST StartAt: %tT\nRequestID: %s\nURL: %s\nRequest: %s ", new Date(), uuid, url, body));
        Response rsp = client.newCall(request).execute();
        assert rsp.body() != null;
        this.rspString = rsp.body().string();
        logger.accept(String.format("POST EndAt: %tT\nRequestID: %s\nTotal Use: %d ms\nURL: %s\nResponse: %s ", new Date(), uuid, (System.currentTimeMillis() - start), url, this.rspString));
        return this;
    }

    public AirOkHttpClientUtil post(Object body) throws IOException {
        return post(AirJsonUtil.toJSONString(body));
    }

    public AirOkHttpClientUtil logger(Consumer<String> logger) {
        this.logger = logger;
        return this;
    }

    public String string() {
        return this.rspString;
    }

    public JSONObject jsonObject() {
        return JSONObject.parseObject(this.rspString);
    }

    public <T> T parse(Class<T> clazz) {
        return AirJsonUtil.parseObject(this.rspString, clazz);
    }

    public JSONObject json() {
        return JSONObject.parseObject(this.rspString);
    }

    public JSONArray jsonArray() {
        return JSONArray.parseArray(this.rspString);
    }

    public <T> T parse(Class<?> collectionClass, Class<?>... elementClasses) {
        return AirJsonUtil.parseObject(this.rspString, null, collectionClass, elementClasses);
    }


    public AirOkHttpClientUtil headers(Map<String, String> headersParams) {

        Headers.Builder headersbuilder = new Headers.Builder();
        if (MapUtils.isNotEmpty(headersParams)) {
            Iterator<String> iterator = headersParams.keySet().iterator();
            String key;
            while (iterator.hasNext()) {
                key = iterator.next();
                headersbuilder.add(key, headersParams.get(key));
            }
        }
        headers = headersbuilder.build();
        return this;
    }
}
