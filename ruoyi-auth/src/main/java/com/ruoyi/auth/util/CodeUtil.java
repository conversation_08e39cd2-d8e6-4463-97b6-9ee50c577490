package com.ruoyi.auth.util;

/**
 * <AUTHOR>
 */
public class CodeUtil {


    /**
     * -------------------------------------------------
     * 是否启用通用短信验证开关 | 域账号是否包含_| 验证方式
     * -------------------------------------------------
     * false                 |包含		    |true
     *---------------------------------------------------
     * false	             |不包含		    |false
     * -------------------------------------------------
     * true	               |包含		        |true
     * -------------------------------------------------
     * true	              |不包含		   |true
     * 验证是否是发送手机短信
     * @param account 账号
     * @param phoneCodeSwitch 开关 true-开启 false-关闭
     * @return true-发送短信 false-不发送短信
     */
    public static boolean ifSendPhoneCode(String account,Boolean phoneCodeSwitch){
        if (phoneCodeSwitch){
            return true;
        }else {
            return account.contains("_");
        }

    }
}
