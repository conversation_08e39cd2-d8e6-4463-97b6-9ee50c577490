

package com.ruoyi.auth.util;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

/**
 * 日期处理工具类
 * 该方法以date为基础，现在接近弃用，推荐使用
 * DateTimeFormatter
 * LocalDate
 * LocalDateTime
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class DateUtils {

    private DateUtils() {
    }

    private static final Logger log = LoggerFactory.getLogger(DateUtils.class);


    /**
     * 时间格式(yyyy-MM-dd)
     */
    public static final String DATE_PATTERN = "yyyy-MM-dd";
    public static final String EN_PATTERN = "ddMMMyy";
    /**
     * 时间格式(yyyy-MM-dd)
     */
    public static final String DATE_CONTINUITY = "yyyyMMdd";

    /**
     * 时间格式(HH:mm)
     */
    public static final String DATE_HH_MM_PATTERN = "HH:mm";
    /**
     * 时间格式(yyyy-MM-dd HH:mm:ss)
     */
    public static final String DATE_TIME_PATTERN = "yyyy-MM-dd HH:mm:ss";
    /**
     * 时间格式(yyyy-MM-dd HH:mm)
     */
    public static final String DATE_SHORT_TIME_PATTERN = "yyyy-MM-dd HH:mm";
    /**
     * 时间格式(yyyyMMddHHmmss)
     */
    public static final String DATE_TIME_CONTINUITY = "yyyyMMddHHmmss";


    /**
     * 日期格式化 日期格式为：yyyy-MM-dd
     *
     * @param date 日期
     * @return 返回yyyy-MM-dd格式日期
     */
    public static String format(Date date) {
        return format(date, DATE_PATTERN);
    }

    public static String formatTime(Date date) {
        return format(date, DATE_TIME_PATTERN);
    }

    /**
     * 日期格式化 日期格式为：yyyy-MM-dd
     *
     * @param date    日期
     * @param pattern 格式，如：DateUtils.DATE_TIME_PATTERN
     * @return 返回yyyy-MM-dd格式日期
     */
    public static String format(Date date, String pattern) {
        if (date != null) {
            SimpleDateFormat df = new SimpleDateFormat(pattern);
            return df.format(date);
        }
        return null;
    }

    /**
     * 日期解析
     *
     * @param date    日期
     * @param pattern 格式，如：DateUtils.DATE_TIME_PATTERN
     * @return 返回Date
     */
    @Deprecated
    public static Date parse(String date, String pattern) {
        try {
            return new SimpleDateFormat(pattern, Locale.US).parse(date);
        } catch (ParseException e) {
            log.error("date parse err.", e);
        }
        return null;
    }

    /**
     * 根据区域，日期解析
     *
     * @param date    日期
     * @param pattern 格式，如：DateUtils.DATE_TIME_PATTERN
     * @return 返回Date
     */
    @Deprecated
    public static Date parse(String date, String pattern, Locale locale) {
        try {
            if (locale == null) {
                locale = Locale.US;
            }
            return new SimpleDateFormat(pattern, locale).parse(date);
        } catch (ParseException e) {
            log.error("date parse err.", e);
        }
        return null;
    }


    /**
     * 两日期相减获取小时
     *
     * @param date1
     * @param date2
     * @return
     */
    public static long getHour(Date date1, Date date2) {
        //除以1000是为了转换成秒
        long between = (date1.getTime() - date2.getTime()) / 1000;
        return between / 60 / 60;
    }

    /**
     * 两日期相减获取年
     *
     * @param date1
     * @param date2
     * @return
     */
    public static long getYears(Date date1, Date date2) {
        //除以1000是为了转换成秒
        long between = (date1.getTime() - date2.getTime()) / 1000;
        return between / 60 / 60 / 24 / 365;
    }

    /**
     * 两日期相减获取天
     *
     * @param date1
     * @param date2
     * @return
     */
    public static long getDays(Date date1, Date date2) {
        //除以1000是为了转换成秒
        long between = (date1.getTime() - date2.getTime()) / 1000;
        return between / 60 / 60 / 24;
    }

}
