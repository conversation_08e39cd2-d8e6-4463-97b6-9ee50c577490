package com.ruoyi.auth.controller;


import com.ruoyi.auth.content.LoginConfig;
import com.ruoyi.auth.form.LoginBody;
import com.ruoyi.auth.service.PhoneMsgService;
import com.ruoyi.auth.service.SendCodeService;
import com.ruoyi.auth.util.AESUtil;
import com.ruoyi.common.core.web.domain.AjaxResult;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * 发送验证码
 *
 * <AUTHOR>
 */
@RestController
public class SendCodeController {

    @Autowired
    private SendCodeService sendCodeService;


    @Autowired
    private LoginConfig loginConfig;

    @SneakyThrows
    @PostMapping("/sendCode")
    public AjaxResult sendCode(@RequestBody LoginBody form, HttpServletRequest request) {
        if ("N".equals(loginConfig.getLoginCheck())){
            return AjaxResult.success("系统暂时关闭验证码验证！可直接输入账号密码登录");
        }
        form.setPassword(AESUtil.decrypt(form.getPassword(),null));
        sendCodeService.sendCode(form,request);
        return AjaxResult.success();
    }


}
