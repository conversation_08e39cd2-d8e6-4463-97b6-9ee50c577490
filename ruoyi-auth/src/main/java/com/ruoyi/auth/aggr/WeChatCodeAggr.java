package com.ruoyi.auth.aggr;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class WeChatCodeAggr {

    private Map<String,Object> body;
    private Integer code;

    public WeChatCodeAggr(String jxUsername, String template) {
        HashMap<String, Object> map = new HashMap<>();
        HashMap<String, Object> data = new HashMap<>();
        List<String> members = new ArrayList<>();
        members.add(jxUsername);
        data.put("numbers",members);
        //生成一个随机4位的整数
        int code = randomCode();
        template = template.replace("{%s}",String.valueOf(code));
        data.put("msg",template);
        map.put("request",data);
        this.body = map;
        this.code = code;
    }
    /**
     * 获取验证码
     * @return
     */
    private static int randomCode(){
        return (int)((Math.random()*9+1)*100000);
    }


}
