# Tomcat
server: 
  port: 9200

# Spring
spring: 
  application:
    # 应用名称
    name: ruoyi-auth
  #profiles:
    # 环境配置
    #active: test
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: 172.22.0.77:8848
        # 命名空间
        namespace: ecs-test
        username: ecscloud
        password: 2FRFTfRk
      config:
        # 配置中心地址
        server-addr: 172.22.0.77:8848
        # 配置文件格式
        file-extension: yml
        # 命名空间
        namespace: ecs-test
        username: ecscloud
        password: 2FRFTfRk
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
