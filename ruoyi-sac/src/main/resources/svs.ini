#签名验签sdk配置文件
#连接配置签名验签ip和端口 端口默认8990
[HT-SVS]
#ip
host=***********
port=8990
#端口 port=10316
;host=*************
;port=10316
#索引
index=1
#私钥权限码
password=12345678
#签名证书编号  d6628db7bb902395
certSn=1973ddfae07
#加密证书编号 数字信封需要
enCertificate=1973ddfae07
#请选择测试接口 1 :数字签名验签 2: p7 带原文 3：p7 不带原文 4 全部 包含数字信封
sign=1
#1:SM2 2:RSA SHA1 3 RSA SHA256
algorithm=1
inData=93cc77fba0df95eccf9f0384da1aad9571bbcfc16ae66572f7dc8cff895d4530
#超时时间
[Timeout]
timeout=15000
#池
[ConnectionPool]
minPoolSize=64
maxPoolSize=64
#ssl相关配置
[SSL]
#是否开启ssl
sslEnabled=false
#证书所在路径 如果开启ssl 需要配置证书相关
cert-base-path=E:\\weixin\\tlcp\\
#根证书
trust-cert: tlcp-intca.crt
trust-key: tlcp-intca.key
#签名证书
client-sign-cert: tlcp-client-sign.crt
client-sign-key: tlcp-client-sign.key
#加密证书
client-enc-cert: tlcp-client-enc.crt
client-enc-key: tlcp-client-enc.key
#广播相关
[DB]
sn=00004102a841
