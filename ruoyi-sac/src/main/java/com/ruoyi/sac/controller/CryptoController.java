package com.ruoyi.sac.controller;

import com.ruoyi.sac.domain.api.*;
import com.ruoyi.sac.domain.response.BatchDecryptResponse;
import com.ruoyi.sac.domain.response.BatchEncryptResponse;
import com.ruoyi.sac.domain.response.DecryptResponse;
import com.ruoyi.sac.domain.response.EncryptResponse;
import com.ruoyi.sac.service.CryptoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/crypto")
public class CryptoController {
    @Autowired
    private CryptoService cryptoService;

    @PostMapping("/encrypt")
    public EncryptResponse encrypt(@RequestBody EncryptRequestForApi request) throws Exception {
        return cryptoService.encryptForApi(request);
    }

    @PostMapping("/decrypt")
    public DecryptResponse decrypt(@RequestBody DecryptRequestForApi request) throws Exception {
        return cryptoService.decryptForApi(request);
    }

    /**
     * 批量加密接口，支持动态字段（如telPhone、credentialsNum等）
     */
    @PostMapping("/batchEncrypt")
    public BatchEncryptResponse batchEncrypt(@RequestBody BatchEncryptRequestForApi request) throws Exception {
        return cryptoService.batchEncryptForApi(request);
    }

    /**
     * 批量解密接口，支持动态字段（如telPhone、credentialsNum等）
     */
    @PostMapping("/batchDecrypt")
    public BatchDecryptResponse batchDecrypt(@RequestBody BatchDecryptRequestForApi request) throws Exception {
        return cryptoService.batchDecryptForApi(request);
    }
} 