package com.ruoyi.sac.service;

import com.haitai.NetSignAgent;
import com.haitai.enums.SignMethodEnum;
import com.haitai.response.ResultCode;
import com.haitai.service.INetSignService;
import com.haitai.util.encoders.Base64;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;

/**
 * 商用密码签名服务类
 * 提供符合GB/T 39786-2021标准的密码应用功能：
 * 1. 数字签名
 * 2. 签名验证
 * 3. P7签名验证
 */
@Slf4j
@Service
public class SignService {

    @Autowired(required = false)
    private INetSignService netSignService;
    
    // 固定参数常量
    private static final int DEFAULT_KEY_INDEX = 1;
    private static final String DEFAULT_KEY_VALUE = "12345678";
    private static final String DEFAULT_CERT_SN = "1973ddfae07";
    
    /**
     * 对数据进行签名
     * 使用SM2/SM3算法进行签名
     */
    public ResultCode<String> signData(String data, int keyIndex, String keyValue) throws Exception {
        if (netSignService == null) {
            log.warn("签名服务未初始化，跳过签名操作");
            return new ResultCode<>(0, "签名服务未初始化", null);
        }
        
        try {
            int signMethod = SignMethodEnum.SGD_SM3_SM2.getValue();
            byte[] keyValueBytes = keyValue.getBytes(StandardCharsets.UTF_8);
            byte[] dataBytes = data.getBytes(StandardCharsets.UTF_8);
            return netSignService.signData(signMethod, keyIndex, keyValueBytes, dataBytes, false);
        } catch (Exception e) {
            log.error("签名失败: {}", e.getMessage());
            return new ResultCode<>(500, "签名失败: " + e.getMessage(), null);
        }
    }
    
    /**
     * 验证签名
     * 使用SM2/SM3算法进行签名验证
     */
    public ResultCode<Boolean> verifySignedData(String certSN, String data, String signature) throws Exception {
        if (netSignService == null) {
            log.warn("签名服务未初始化，跳过签名验证");
            return new ResultCode<>(0, "签名服务未初始化", true);
        }
        
        try {
            int type = 2; // 使用证书序列号
            byte[] dataBytes = data.getBytes(StandardCharsets.UTF_8);
            byte[] signatureBytes = Base64.decode(signature);
            int signMethod = SignMethodEnum.SGD_SM3_SM2.getValue();
            return netSignService.verifySignedData(type, null, certSN, dataBytes, signatureBytes, 0, signMethod, false);
        } catch (Exception e) {
            log.error("验证签名失败: {}", e.getMessage());
            return new ResultCode<>(500, "验证签名失败: " + e.getMessage(), false);
        }
    }
    
    /**
     * P7签名验证
     * 使用SM2/SM3算法进行P7签名验证
     */
    public ResultCode<Boolean> detachedVerify(String random, String p7SignData) throws Exception {
        if (netSignService == null) {
            log.warn("签名服务未初始化，跳过P7签名验证");
            return new ResultCode<>(0, "签名服务未初始化", true);
        }
        
        try {
            int signMethod = SignMethodEnum.SGD_SM3_SM2.getValue();
            return netSignService.detachedVerify(random.getBytes(StandardCharsets.UTF_8), p7SignData, signMethod);
        } catch (Exception e) {
            log.error("P7签名验证失败: {}", e.getMessage());
            return new ResultCode<>(500, "P7签名验证失败: " + e.getMessage(), false);
        }
    }

    /**
     * 封装：只需传data即可签名，keyIndex和keyValue为固定值
     * @param data
     * @return
     * @throws Exception
     */
    public ResultCode<String> signData(String data) throws Exception {
        return signData(data, DEFAULT_KEY_INDEX, DEFAULT_KEY_VALUE);
    }

    /**
     * 封装：只需传data和signature即可验签，certSN为固定值
     * @param data
     * @param signature
     * @return
     * @throws Exception
     */
    public ResultCode<Boolean> verifySignedData(String data, String signature) throws Exception {
        return verifySignedData(DEFAULT_CERT_SN, data, signature);
    }
} 