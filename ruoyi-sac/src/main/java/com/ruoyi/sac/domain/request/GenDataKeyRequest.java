package com.ruoyi.sac.domain.request;

import lombok.Data;

/**
 * 生成数据密钥请求实体
 */
@Data
public class GenDataKeyRequest {
    /**
     * 业务ID，使用当前调用时间，格式为时间戳（建议业务系统进行日志记录，方便根据transId排查问题）
     */
    private String transId;

    /**
     * 编码类型:十六进制编码(HEX),BASE64编码(BASE64)，无编码(NON),默认NON
     */
    private String encode;

    /**
     * 密钥类型,默认SM4
     */
    private String keyType;

    /**
     * 密钥长度（单位bit），默认128
     */
    private Integer keyLength;

    /**
     * 密钥别名，编码方式由encode指定
     */
    private String keyAlias;
} 