package com.ruoyi.sac.domain.response;

import lombok.Data;

@Data
public class DecryptResponse {
    /**
     * 响应码，根据code判断是否成功,200为成功
     */
    private Integer code;

    /**
     * 响应信息，当失败时返回原因
     */
    private String msg;

    /**
     * 方法调用时传入的transId，作为结果原样返回
     */
    private String transId;

    /**
     * 响应数据
     */
    private DecryptData data;

    /**
     * 加密响应数据
     */
    @Data
    public static class DecryptData {

        /**
         * 编码类型:十六进制编码(HEX),BASE64编码(BASE64)
         */
        private String encode;

        private String decData;

        /**
         * 数据密钥唯一标识
         */
        private String keyId;

    }
}
