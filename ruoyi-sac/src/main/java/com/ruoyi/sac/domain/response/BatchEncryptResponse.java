package com.ruoyi.sac.domain.response;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class BatchEncryptResponse {
    /**
     * 响应码，根据code判断是否成功,200为成功
     */
    private Integer code;

    /**
     * 响应信息，当失败时返回原因
     */
    private String msg;

    /**
     * 方法调用时传入的transId，作为结果原样返回
     */
    private String transId;

    private BatchEncryptResponse.BatchEncryptData data;


    @Data
    public static class BatchEncryptData {
        private String encode;
        private String padding;
        private List<BatchData> encData;
    }

    @Data
    public static class BatchData {
        private String tag;
        private String keyId;
        private Map<String, String> extraFields = new HashMap<>();

        @JsonAnySetter
        public void setExtraField(String key, String value) {
            if (!"tag".equals(key) && !"keyId".equals(key)) {
                extraFields.put(key, value);
            }
        }

        @JsonAnyGetter
        public Map<String, String> getExtraFields() {
            return extraFields;
        }
    }
}
