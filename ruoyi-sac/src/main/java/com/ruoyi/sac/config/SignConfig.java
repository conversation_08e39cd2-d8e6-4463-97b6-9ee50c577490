package com.ruoyi.sac.config;

import com.haitai.NetSignAgent;
import com.haitai.service.INetSignService;
import com.haitai.service.impl.NetSignServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.BeanInitializationException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.io.ClassPathResource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Slf4j
@Configuration
public class SignConfig {

    @Value(value = "${sac.sign.enable:false}")
    private boolean enableSignService;

    @Bean
    public NetSignAgent netSignAgent() {
        NetSignAgent netSignAgent = NetSignAgent.getInstance();
        if (!enableSignService) {
            log.info("签名服务未启用");
            return netSignAgent;
        } else {
            log.info("签名服务已启用");
        }
        try {
            ClassPathResource resource = new ClassPathResource("svs.ini");
            
            // 创建临时文件
            Path tempFile = Files.createTempFile("svs", ".ini");
            try (InputStream inputStream = resource.getInputStream();
                 FileOutputStream outputStream = new FileOutputStream(tempFile.toFile())) {
                byte[] buffer = new byte[1024];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
            }
            
            // 使用临时文件初始化
            netSignAgent.initialize(tempFile.toFile().getAbsolutePath());
            
            // 删除临时文件
            tempFile.toFile().deleteOnExit();
            return netSignAgent;

        } catch (Exception e) {
            log.error("初始化签名服务失败: {}", e.getMessage());
            return netSignAgent;
        }
    }
    
    @Bean
    public INetSignService netSignService(NetSignAgent netSignAgent) {

        try {
            return netSignAgent.getNetSignServiceInstance();
        } catch (Exception e) {
            log.error("获取签名服务实例失败: {}", e.getMessage());
            return new NetSignServiceImpl();
        }
    }
} 