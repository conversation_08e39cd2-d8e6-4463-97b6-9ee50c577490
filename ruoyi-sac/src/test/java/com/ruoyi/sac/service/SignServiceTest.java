package com.ruoyi.sac.service;

import com.haitai.NetSignAgent;
import com.haitai.enums.SignMethodEnum;
import com.haitai.response.ResultCode;
import com.haitai.service.INetSignService;
import com.haitai.util.encoders.Base64;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;
import java.nio.charset.StandardCharsets;

import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

@Configuration
@ComponentScan(basePackages = "com.ruoyi.sac")
class SignServiceTestConfig {
    // 测试配置类
}

@RunWith(SpringRunner.class)
@SpringBootTest(classes = SignServiceTestConfig.class)
public class SignServiceTest {

    @Autowired
    private SignService signService;

    private static final String TEST_DATA = "12345678";
    private static final int KEY_INDEX = 1;
    private static final String KEY_VALUE = "12345678";
    private static final String CERT_SN = "1973ddfae07";
    private static final String RANDOM = "18838f8b";
    private static final String P7_SIGN_DATA = "MIIDRQYKKoEcz1UGAQQCAqCCAzUwggMxAgEBMQ4wDAYIKoEcz1UBgxEFADAYBgoqgRzPVQYBBAIBoAoECDE4ODM4ZjhioIICGTCCAhUwggG8oAMCAQICCQDzCp2iDR+a nzAKBggqgRzPVQGDdTBoMRAwDgYDVQQDDAdST09ULUNBMRswGQYDVQQLDBLlr4bn oIHlupTnlKjnoJTlj5ExFTATBgNVBAoMDOa1t+azsOaWueWchjEPMA0GA1UEBwwG5YyX5LqsMQ8wDQYDVQQIDAbljJfkuqwwHhcNMjQwNTIwMDc1NDU0WhcNMjcwNTIwMDc1NDU0WjBoMREwDwYDVQQDDAh0ZXN0c2VhbDEPMA0GA1UECwwG5a6J5YWoMQ8wDQYDVQQKDAbmtbfms7AxDzANBgNVBAcMBuWMl+S6rDEPMA0GA1UECAwG5YyX5LqsMQ8wDQYDVQQGDAbkuK3lm70wWTATBgcqhkjOPQIBBggqgRzPVQGCLQNCAATRhFPEAt5wJf+F8K9tPmlR87pT3HYPbOieRaZKfFClm4NIUpL/UxZlNSt7xO70xPSMQJ/YPizkPQI3cgDd1iCOo08wTTAfBgNVHSMEGDAWgBRLcDpT2tQhcWrzH9qJVs6j0c6QKDAdBgNVHQ4EFgQUwe9u88WkS8U77yIAkE0LNIv68dgwCwYDVR0PBAQDAgHGMAoGCCqBHM9VAYN1A0cAMEQCIAK0SnM+Rv/OefW6yOP4tlfkpWzzKa8YrTyV1hZOhbzHAiBMEvG5v5LgUCQMEzinjMx6UiPfIin5Lvw/XEkmuA+lEzGB5DCB4QIBATB1MGgxEDAOBgNVBAMMB1JPT1QtQ0ExGzAZBgNVBAsMEuWvhueggeW6lOeUqOeglOWPkTEVMBMGA1UECgwM5rW35rOw5pa55ZyGMQ8wDQYDVQQHDAbljJfkuqwxDzANBgNVBAgMBuWMl+S6rAIJAPMKnaINH5qfMAwGCCqBHM9VAYMRBQAwDQYJKoEcz1UBgi0BBQAESDBGAiEAgHx38vq9ixPdWSwpcSNblJn3WXTdR21ewp1ClawRG6UCIQDIfMdrb+M77O/Z6C9t07Dkp6GHHYDh1zlPa9ePgjmq8w==";

    @Before
    public void setUp() throws Exception {
        // 初始化签名服务
        NetSignAgent netSignAgent = NetSignAgent.getInstance();
        ClassPathResource resource = new ClassPathResource("svs.ini");
        File configFile = resource.getFile();
        netSignAgent.initialize(configFile.getAbsolutePath());
    }

    @Test
    public void testSignData() throws Exception {
        // 测试数字签名
        ResultCode<String> resultCode = signService.signData(TEST_DATA, KEY_INDEX, KEY_VALUE);
        assertNotNull("签名结果不应为空", resultCode);
        assertEquals("签名应该成功", Integer.valueOf(0), resultCode.getCode());
        assertNotNull("签名数据不应为空", resultCode.getData());
    }

    @Test
    public void testVerifySignedData() throws Exception {
        // 先进行签名
        ResultCode<String> signResult = signService.signData(TEST_DATA, KEY_INDEX, KEY_VALUE);
        assertNotNull("签名结果不应为空", signResult);
        assertEquals("签名应该成功", Integer.valueOf(0), signResult.getCode());

        // 验证签名
        ResultCode<Boolean> verifyResult = signService.verifySignedData(CERT_SN, TEST_DATA, signResult.getData());
        assertNotNull("验证结果不应为空", verifyResult);
        assertEquals("验证应该成功", Integer.valueOf(0), verifyResult.getCode());
        assertTrue("验证结果应该为true", verifyResult.getData());
    }

    @Test
    public void testDetachedVerify() throws Exception {
        // 测试P7签名验证
        ResultCode<Boolean> verifyResult = signService.detachedVerify(RANDOM, P7_SIGN_DATA);
        assertNotNull("验证结果不应为空", verifyResult);
        assertEquals("验证应该成功", Integer.valueOf(0), verifyResult.getCode());
        assertTrue("验证结果应该为true", verifyResult.getData());
    }
} 