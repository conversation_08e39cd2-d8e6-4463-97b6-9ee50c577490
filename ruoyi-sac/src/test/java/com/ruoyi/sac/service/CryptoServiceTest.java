package com.ruoyi.sac.service;

import com.ruoyi.sac.domain.api.*;
import com.ruoyi.sac.domain.response.BatchDecryptResponse;
import com.ruoyi.sac.domain.response.BatchEncryptResponse;
import com.ruoyi.sac.domain.response.DecryptResponse;
import com.ruoyi.sac.domain.response.EncryptResponse;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.*;

import static org.junit.Assert.*;

/**
 * CryptoService测试类
 * 测试加密和解密功能的对应性（实际调用CryptoService，不使用Mock）
 */
@Configuration
@ComponentScan(basePackages = "com.ruoyi.sac")
class CryptoServiceTestConfig {
    // 测试配置类
}

@RunWith(SpringRunner.class)
@SpringBootTest(classes = CryptoServiceTestConfig.class)
@ContextConfiguration(classes = CryptoServiceTestConfig.class)
@TestPropertySource(properties = {
    "crypto.service.base-url=http://***********:18849",
    "crypto.service.authorization=Digest algo=SM3 realm=f8b7f5167a71440f9a2092b42de9d694"
})
public class CryptoServiceTest {

    @Autowired
    private CryptoService cryptoService;

    // 测试数据
    private static final String TEST_PLAIN_TEXT = "48656c6c6f20576f726c64"; // "Hello World" 的HEX编码
    private static final String TEST_CIPHER_TEXT = "a1b2c3d4e5f6789012345678901234567890abcdef"; // 模拟密文
    private static final String TEST_KEY_ID = "test-key-id-12345";

    @Before
    public void setUp() {
        // 初始化测试数据
        assertNotNull("CryptoService应该被正确注入", cryptoService);
    }

    /**
     * 测试单个数据加密解密的对应性
     * 实际调用加密服务进行加密，然后解密，验证结果的一致性
     */
    @Test
    public void testEncryptDecryptCorrespondence() throws Exception {

        // 准备测试数据
        EncryptRequestForApi encryptRequest = new EncryptRequestForApi();
        encryptRequest.setData(TEST_PLAIN_TEXT);

        // 第一步：执行加密
        EncryptResponse encryptResponse = cryptoService.encryptForApi(encryptRequest);

        // 验证加密结果
        assertNotNull("加密响应不应为空", encryptResponse);
        assertEquals("加密应该成功", Integer.valueOf(200), encryptResponse.getCode());
        assertNotNull("加密数据不应为空", encryptResponse.getData());

        EncryptResponse.EncryptData encryptedData = encryptResponse.getData();
        assertNotNull("加密后的密文不应为空", encryptedData.getEncData());
        assertNotEquals("密文应该与明文不同", TEST_PLAIN_TEXT, encryptedData.getEncData());

        // 第二步：使用加密结果进行解密
        DecryptRequestForApi decryptRequest = new DecryptRequestForApi();
        decryptRequest.setData(encryptedData.getEncData());

        DecryptResponse decryptResponse = cryptoService.decryptForApi(decryptRequest);

        // 验证解密结果
        assertNotNull("解密响应不应为空", decryptResponse);
        assertEquals("解密应该成功", Integer.valueOf(200), decryptResponse.getCode());
        assertNotNull("解密数据不应为空", decryptResponse.getData());

        // 验证加密解密的对应性
        DecryptResponse.DecryptData decryptedData = (DecryptResponse.DecryptData) decryptResponse.getData();
        assertEquals("解密后应该得到原始明文", TEST_PLAIN_TEXT, decryptedData.getDecData());

        System.out.println("单个数据加密解密测试通过");
        System.out.println("原始明文: " + TEST_PLAIN_TEXT);
        System.out.println("加密密文: " + encryptedData.getEncData());
        System.out.println("解密明文: " + decryptedData.getDecData());

    }

    /**
     * 测试批量数据加密解密的对应性
     * 实际调用加密服务进行批量加密，然后解密，验证结果的一致性
     */
    @Test
    public void testBatchEncryptDecryptCorrespondence() throws Exception {

        // 准备批量测试数据
        BatchEncryptRequestForApi batchEncryptRequest = new BatchEncryptRequestForApi();
        List<Map<String, String>> dataList = new ArrayList<>();

        Map<String, String> data1 = new HashMap<>();
        data1.put("telPhone", "31383833383838383838"); // "18838888888" 的HEX编码
        data1.put("tag", "user1");

        Map<String, String> data2 = new HashMap<>();
//        data2.put("credentialsNum", "34313032303139393031303131323334"); // "410219901011234" 的HEX编码
        data2.put("telPhone", "34313032303139393031303131323334"); // "410219901011234" 的HEX编码
        data2.put("tag", "user2");

        dataList.add(data1);
        dataList.add(data2);
        batchEncryptRequest.setDataList(dataList);

        // 第一步：执行批量加密
        BatchEncryptResponse batchEncryptResponse = cryptoService.batchEncryptForApi(batchEncryptRequest);

        // 验证批量加密结果
        assertNotNull("批量加密响应不应为空", batchEncryptResponse);
        assertEquals("批量加密应该成功", Integer.valueOf(200), batchEncryptResponse.getCode());
        assertNotNull("批量加密数据不应为空", batchEncryptResponse.getData());

        // 获取批量加密结果
        BatchEncryptResponse.BatchEncryptData encryptData = batchEncryptResponse.getData();
        assertNotNull("批量加密数据不应为空", encryptData);
        assertNotNull("批量加密结果列表不应为空", encryptData.getEncData());
        assertEquals("加密结果数量应该与输入数据相同", 2, encryptData.getEncData().size());

        // 第二步：准备批量解密请求
        BatchDecryptRequestForApi batchDecryptRequest = new BatchDecryptRequestForApi();
        List<Map<String, String>> decryptDataList = new ArrayList<>();

        for (BatchEncryptResponse.BatchData encryptedItem : encryptData.getEncData()) {
            Map<String, String> decryptItem = new HashMap<>();
            // 添加tag字段
            if (encryptedItem.getTag() != null) {
                decryptItem.put("tag", encryptedItem.getTag());
            }
            // 添加extraFields中的加密数据
            decryptItem.putAll(encryptedItem.getExtraFields());
            decryptDataList.add(decryptItem);
        }
        batchDecryptRequest.setDataList(decryptDataList);

        // 第三步：执行批量解密
        BatchDecryptResponse batchDecryptResponse = cryptoService.batchDecryptForApi(batchDecryptRequest);

        // 验证批量解密结果
        assertNotNull("批量解密响应不应为空", batchDecryptResponse);
        assertEquals("批量解密应该成功", Integer.valueOf(200), batchDecryptResponse.getCode());
        assertNotNull("批量解密数据不应为空", batchDecryptResponse.getData());

        // 获取批量解密结果
        BatchDecryptResponse.BatchDecryptData decryptData = batchDecryptResponse.getData();
        assertNotNull("批量解密数据不应为空", decryptData);
        assertNotNull("批量解密结果列表不应为空", decryptData.getEncData());
        assertEquals("解密结果数量应该与原始数据相同", 2, decryptData.getEncData().size());

        // 验证加密解密的对应性
        for (int i = 0; i < dataList.size(); i++) {
            Map<String, String> originalData = dataList.get(i);
            BatchDecryptResponse.BatchData decryptedData = decryptData.getEncData().get(i);

            // 验证tag字段保持不变
            assertEquals("tag字段应该保持不变", originalData.get("tag"), decryptedData.getTag());

            // 验证数据字段解密后与原始数据相同
            for (Map.Entry<String, String> entry : originalData.entrySet()) {
                if (!"tag".equals(entry.getKey())) {
                    assertEquals("解密后的" + entry.getKey() + "应该与原始数据相同",
                            entry.getValue(), decryptedData.getExtraFields().get(entry.getKey()));
                }
            }
        }

        System.out.println("批量数据加密解密测试通过");
        System.out.println("原始数据数量: " + dataList.size());
        System.out.println("加密结果数量: " + encryptData.getEncData().size());
        System.out.println("解密结果数量: " + decryptData.getEncData().size());


    }

    /**
     * 测试请求对象的基本属性设置
     */
    @Test
    public void testRequestObjectProperties() {
        // 测试EncryptRequestForApi
        EncryptRequestForApi encryptRequest = new EncryptRequestForApi();
        encryptRequest.setData(TEST_PLAIN_TEXT);
        assertEquals("加密请求数据应该正确设置", TEST_PLAIN_TEXT, encryptRequest.getData());

        // 测试DecryptRequestForApi
        DecryptRequestForApi decryptRequest = new DecryptRequestForApi();
        decryptRequest.setData(TEST_CIPHER_TEXT);
        assertEquals("解密请求数据应该正确设置", TEST_CIPHER_TEXT, decryptRequest.getData());

        // 测试BatchEncryptRequestForApi
        BatchEncryptRequestForApi batchEncryptRequest = new BatchEncryptRequestForApi();
        List<Map<String, String>> dataList = new ArrayList<>();
        
        Map<String, String> data1 = new HashMap<>();
        data1.put("telPhone", TEST_PLAIN_TEXT);
        data1.put("tag", "user1");
        dataList.add(data1);
        
        batchEncryptRequest.setDataList(dataList);
        assertNotNull("批量加密请求数据列表不应为空", batchEncryptRequest.getDataList());
        assertEquals("批量加密请求数据列表大小应该正确", 1, batchEncryptRequest.getDataList().size());
        assertEquals("批量加密请求数据内容应该正确", TEST_PLAIN_TEXT, 
                batchEncryptRequest.getDataList().get(0).get("telPhone"));
    }

    /**
     * 测试响应对象的基本属性设置
     */
    @Test
    public void testResponseObjectProperties() {
        // 测试EncryptResponse
        EncryptResponse response = new EncryptResponse();
        response.setCode(200);
        response.setMsg("success");
        response.setTransId("12345");

        assertEquals("响应码应该正确设置", Integer.valueOf(200), response.getCode());
        assertEquals("响应消息应该正确设置", "success", response.getMsg());
        assertEquals("事务ID应该正确设置", "12345", response.getTransId());

        // 测试EncryptResponse.EncryptData
        EncryptResponse.EncryptData encryptData = new EncryptResponse.EncryptData();
        encryptData.setEncData(TEST_CIPHER_TEXT);
        encryptData.setKeyId(TEST_KEY_ID);
        encryptData.setEncode("HEX");
        encryptData.setPadding("PKCS7Padding");

        assertEquals("加密数据应该正确设置", TEST_CIPHER_TEXT, encryptData.getEncData());
        assertEquals("密钥ID应该正确设置", TEST_KEY_ID, encryptData.getKeyId());
        assertEquals("编码类型应该正确设置", "HEX", encryptData.getEncode());
        assertEquals("填充方式应该正确设置", "PKCS7Padding", encryptData.getPadding());

        response.setData(encryptData);
        assertNotNull("响应数据不应为空", response.getData());
        assertTrue("响应数据应该是EncryptData类型", response.getData() instanceof EncryptResponse.EncryptData);
    }

    /**
     * 测试数据验证逻辑
     */
    @Test
    public void testDataValidation() {
        // 测试空数据处理
        EncryptRequestForApi emptyRequest = new EncryptRequestForApi();
        emptyRequest.setData(null);
        assertNull("空数据应该保持为null", emptyRequest.getData());

        emptyRequest.setData("");
        assertEquals("空字符串应该保持为空字符串", "", emptyRequest.getData());

        // 测试数据一致性验证
        String originalData = TEST_PLAIN_TEXT;
        String processedData = originalData; // 模拟处理过程
        assertEquals("处理前后数据应该一致", originalData, processedData);

        // 测试批量数据一致性
        List<Map<String, String>> originalBatchData = new ArrayList<>();
        Map<String, String> item1 = new HashMap<>();
        item1.put("field1", "value1");
        item1.put("tag", "tag1");
        originalBatchData.add(item1);

        List<Map<String, String>> processedBatchData = new ArrayList<>(originalBatchData);
        assertEquals("批量数据处理前后大小应该一致", originalBatchData.size(), processedBatchData.size());
        assertEquals("批量数据内容应该一致", originalBatchData.get(0).get("field1"), 
                processedBatchData.get(0).get("field1"));
        assertEquals("批量数据标签应该一致", originalBatchData.get(0).get("tag"), 
                processedBatchData.get(0).get("tag"));
    }
}
