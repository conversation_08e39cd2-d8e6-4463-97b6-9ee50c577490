<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>ruoyi</artifactId>
        <groupId>com.ruoyi</groupId>
        <version>3.6.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>ruoyi-sac</artifactId>

    <description>
        Security Assessment of Commercial Cryptography Module
    </description>

    <dependencies>

        <!-- Spring Boot Test -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- JUnit -->
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.13.2</version>
            <scope>test</scope>
        </dependency>

        <!-- Mockito Inline for static mocking -->
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-inline</artifactId>
            <version>4.6.1</version>
            <scope>test</scope>
        </dependency>

        <!-- 海泰签名SDK -->
        <dependency>
            <groupId>com.haitai</groupId>
            <artifactId>haitai-net-sign-sdk</artifactId>
            <version>1.0.9.20240913</version>
        </dependency>

        <!-- SLF4J日志框架 -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <version>1.7.36</version>
        </dependency>

        <!-- Kona SSL相关依赖 -->
        <dependency>
            <groupId>com.tencent.kona</groupId>
            <artifactId>kona-ssl</artifactId>
            <version>1.0.8</version>
        </dependency>
        <dependency>
            <groupId>com.tencent.kona</groupId>
            <artifactId>kona-provider</artifactId>
            <version>1.0.8</version>
        </dependency>
        <dependency>
            <groupId>com.tencent.kona</groupId>
            <artifactId>kona-pkix</artifactId>
            <version>1.0.8</version>
        </dependency>
        <dependency>
            <groupId>com.tencent.kona</groupId>
            <artifactId>kona-crypto</artifactId>
            <version>1.0.8</version>
        </dependency>

        <!-- 海泰公共组件 -->
        <dependency>
            <groupId>com.haitai</groupId>
            <artifactId>haitai-common-pool</artifactId>
            <version>2.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.haitai</groupId>
            <artifactId>haitai-common-jce</artifactId>
            <version>3.0.2.20230918</version>
        </dependency>

        <!-- Google Guava工具库 -->
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>20.0</version>
        </dependency>

        <!-- LMAX Disruptor -->
        <dependency>
            <groupId>com.lmax</groupId>
            <artifactId>disruptor</artifactId>
            <version>1.2.15</version>
        </dependency>

        <!-- Apache Commons Lang3 -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.12.0</version>
        </dependency>
        
        <!-- Apache Commons Lang3 -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>

        <!-- Apache HttpClient -->
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.5.13</version>
        </dependency>

        <!-- Jackson -->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>2.13.4.2</version>
        </dependency>

        <!-- Lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.22</version>
            <scope>provided</scope>
        </dependency>

        <!-- Spring Boot Web -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
    </dependencies>

    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*.ini</include>
                </includes>
            </resource>
        </resources>
    </build>

</project> 