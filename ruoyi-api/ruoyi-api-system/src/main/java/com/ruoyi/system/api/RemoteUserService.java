package com.ruoyi.system.api;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.constant.ServiceNameConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.system.api.domain.SysUser;
import com.ruoyi.system.api.factory.RemoteUserFallbackFactory;
import com.ruoyi.system.api.model.LoginUser;
import com.ruoyi.system.api.dto.SacSignaturesReqDto;
import com.ruoyi.system.api.dto.VerifySignatureReqDto;

import java.util.List;

/**
 * 用户服务
 * 
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteUserService", value = ServiceNameConstants.SYSTEM_SERVICE, fallbackFactory = RemoteUserFallbackFactory.class)
public interface RemoteUserService
{
    /**
     * 通过用户名查询用户信息
     *
     * @param username 用户名
     * @param source 请求来源
     * @return 结果
     */
    @GetMapping("/user/info/{username}")
    public R<LoginUser> getUserInfo(@PathVariable("username") String username, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 注册用户信息
     *
     * @param sysUser 用户信息
     * @param source 请求来源
     * @return 结果
     */
    @PostMapping("/user/register")
    public R<Boolean> registerUserInfo(@RequestBody SysUser sysUser, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 管理员角色签名
     * @param reqDto 签名请求
     * @param source 请求来源
     * @return 结果
     */
    @PostMapping("/sac/signatures/update")
    R<Boolean> updateSignatures(@RequestBody SacSignaturesReqDto reqDto, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 验证签名
     * @param reqDto 验签请求
     * @param source 请求来源
     * @return 结果
     */
    @PostMapping("/sac/signatures/verify")
    R<Boolean> verifySignature(@RequestBody VerifySignatureReqDto reqDto, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 根据用户ID查询角色ID列表
     * @param userId 用户ID
     * @param source 请求来源
     * @return 角色ID列表
     */
    @GetMapping("/user/role/list/{userId}")
    R<List<Long>> selectRoleListByUserId(@PathVariable("userId") Long userId, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 根据角色ID查询菜单ID列表
     * @param roleId 角色ID
     * @param source 请求来源
     * @return 菜单ID列表
     */
    @GetMapping("/user/role/menu/list/{roleId}")
    R<List<Long>> selectMenuListByRoleId(@PathVariable("roleId") Long roleId, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 解密用户密码
     * @param encryptedPassword 加密的密码
     * @param source 请求来源
     * @return 解密后的密码
     */
    @PostMapping("/user/password/decrypt")
    R<String> decryptPassword(@RequestBody String encryptedPassword, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);
}
