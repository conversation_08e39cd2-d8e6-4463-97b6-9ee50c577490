package com.ruoyi.system.api.factory;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.system.api.RemoteUserService;
import com.ruoyi.system.api.domain.SysUser;
import com.ruoyi.system.api.model.LoginUser;

/**
 * 用户服务降级处理
 * 
 * <AUTHOR>
 */
@Component
public class RemoteUserFallbackFactory implements FallbackFactory<RemoteUserService>
{
    private static final Logger log = LoggerFactory.getLogger(RemoteUserFallbackFactory.class);

    @Override
    public RemoteUserService create(Throwable throwable)
    {
        log.error("用户服务调用失败:{}", throwable.getMessage());
        return new RemoteUserService()
        {
            @Override
            public R<LoginUser> getUserInfo(String username, String source)
            {
                return R.fail("获取用户失败:" + throwable.getMessage());
            }

            @Override
            public R<Boolean> registerUserInfo(SysUser sysUser, String source)
            {
                return R.fail("注册用户失败:" + throwable.getMessage());
            }

            @Override
            public R<Boolean> updateSignatures(com.ruoyi.system.api.dto.SacSignaturesReqDto reqDto, String source) {
                return R.fail("签名失败:" + throwable.getMessage());
            }

            @Override
            public R<Boolean> verifySignature(com.ruoyi.system.api.dto.VerifySignatureReqDto reqDto, String source) {
                return R.fail("验签失败:" + throwable.getMessage());
            }

            @Override
            public R<java.util.List<Long>> selectRoleListByUserId(Long userId, String source) {
                return R.fail("查询用户角色列表失败:" + throwable.getMessage());
            }

            @Override
            public R<java.util.List<Long>> selectMenuListByRoleId(Long roleId, String source) {
                return R.fail("查询角色菜单列表失败:" + throwable.getMessage());
            }

            @Override
            public R<String> decryptPassword(String encryptedPassword, String source) {
                return R.fail("密码解密失败:" + throwable.getMessage());
            }
        };
    }
}
